"""
交易复盘工具 - 修复版GUI
基于深度诊断结果修复的桌面应用版本
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List

class TradingAppGUIFixed:
    """修复版交易复盘工具GUI"""
    
    def __init__(self):
        print("🚀 启动交易复盘工具 - 修复版GUI")
        
        # 初始化数据库
        self.init_database()
        
        # 创建主窗口
        self.create_main_window()
        
        # 创建界面
        self.create_interface()
        
        # 初始化数据
        self.init_default_values()
        
        print("✅ GUI应用初始化完成")
    
    def init_database(self):
        """初始化数据库"""
        try:
            self.db_path = "trading_data.db"
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row
            
            # 创建表
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    position_size REAL NOT NULL,
                    direction TEXT NOT NULL,
                    result_pct REAL NOT NULL,
                    entry_time DATETIME NOT NULL,
                    exit_time DATETIME NOT NULL,
                    entry_fee_pct REAL NOT NULL DEFAULT 0.05,
                    exit_fee_pct REAL NOT NULL DEFAULT 0.05,
                    instrument TEXT DEFAULT '',
                    notes TEXT DEFAULT '',
                    gross_pnl REAL,
                    entry_fees REAL,
                    exit_fees REAL,
                    total_fees REAL,
                    net_pnl REAL,
                    duration_minutes REAL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            self.conn.commit()
            print("✅ 数据库初始化成功")
            
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            raise
    
    def create_main_window(self):
        """创建主窗口"""
        try:
            # 设置环境变量消除警告
            os.environ['TK_SILENCE_DEPRECATION'] = '1'
            
            print("创建主窗口...")
            self.root = tk.Tk()
            self.root.title("📊 交易复盘工具 - 桌面版")
            
            # 设置窗口大小和位置
            window_width = 1000
            window_height = 700
            
            # 获取屏幕尺寸并居中
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2
            
            self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
            self.root.minsize(800, 600)
            
            # 设置窗口图标和属性
            self.root.configure(bg='#f0f0f0')
            
            # 确保窗口正确显示
            self.root.update_idletasks()
            self.root.deiconify()
            
            print("✅ 主窗口创建成功")
            
        except Exception as e:
            print(f"❌ 主窗口创建失败: {e}")
            raise
    
    def create_interface(self):
        """创建用户界面"""
        try:
            print("创建用户界面...")
            
            # 创建主标题
            self.create_header()
            
            # 创建主要内容区域
            self.create_main_content()
            
            # 创建状态栏
            self.create_status_bar()
            
            print("✅ 用户界面创建成功")
            
        except Exception as e:
            print(f"❌ 用户界面创建失败: {e}")
            raise
    
    def create_header(self):
        """创建标题区域"""
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        header_frame.pack(fill='x', pady=(0, 10))
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(
            header_frame,
            text="📊 交易复盘工具",
            font=('Arial', 20, 'bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(expand=True)
        
        subtitle_label = tk.Label(
            header_frame,
            text="专业的交易记录与分析平台",
            font=('Arial', 12),
            fg='#ecf0f1',
            bg='#2c3e50'
        )
        subtitle_label.pack()
    
    def create_main_content(self):
        """创建主要内容区域"""
        # 创建主框架
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # 左侧：输入区域
        left_frame = tk.LabelFrame(
            main_frame, 
            text="📝 交易信息录入", 
            font=('Arial', 12, 'bold'),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        self.create_input_section(left_frame)
        
        # 右侧：预览和统计区域
        right_frame = tk.Frame(main_frame, bg='#f0f0f0')
        right_frame.pack(side='right', fill='both', expand=True)
        
        self.create_preview_section(right_frame)
        self.create_stats_section(right_frame)
    
    def create_input_section(self, parent):
        """创建输入区域"""
        # 仓位大小
        pos_frame = tk.LabelFrame(parent, text="💰 仓位大小", bg='#f0f0f0')
        pos_frame.pack(fill='x', pady=5, padx=10)
        
        # 快速按钮
        btn_frame = tk.Frame(pos_frame, bg='#f0f0f0')
        btn_frame.pack(fill='x', pady=5)
        
        self.position_var = tk.StringVar(value="10000")
        
        positions = [("5千", "5000"), ("1万", "10000"), ("2万", "20000"), ("5万", "50000")]
        for text, value in positions:
            btn = tk.Button(
                btn_frame,
                text=text,
                command=lambda v=value: self.set_position(v),
                bg='#3498db',
                fg='white',
                font=('Arial', 10),
                width=8
            )
            btn.pack(side='left', padx=2)
        
        # 自定义输入
        input_frame = tk.Frame(pos_frame, bg='#f0f0f0')
        input_frame.pack(fill='x', pady=5)
        
        tk.Label(input_frame, text="自定义:", bg='#f0f0f0').pack(side='left')
        self.position_entry = tk.Entry(input_frame, textvariable=self.position_var, width=15)
        self.position_entry.pack(side='left', padx=5)
        self.position_entry.bind('<KeyRelease>', self.on_input_change)
        tk.Label(input_frame, text="元", bg='#f0f0f0').pack(side='left')
        
        # 交易方向
        dir_frame = tk.LabelFrame(parent, text="📈 交易方向", bg='#f0f0f0')
        dir_frame.pack(fill='x', pady=5, padx=10)
        
        self.direction_var = tk.StringVar(value="Long")
        
        radio_frame = tk.Frame(dir_frame, bg='#f0f0f0')
        radio_frame.pack(pady=5)
        
        tk.Radiobutton(
            radio_frame,
            text="📈 做多 (Long)",
            variable=self.direction_var,
            value="Long",
            command=self.on_input_change,
            bg='#f0f0f0',
            font=('Arial', 10)
        ).pack(side='left', padx=10)
        
        tk.Radiobutton(
            radio_frame,
            text="📉 做空 (Short)",
            variable=self.direction_var,
            value="Short",
            command=self.on_input_change,
            bg='#f0f0f0',
            font=('Arial', 10)
        ).pack(side='left', padx=10)
        
        # 交易结果
        result_frame = tk.LabelFrame(parent, text="🎯 交易结果", bg='#f0f0f0')
        result_frame.pack(fill='x', pady=5, padx=10)
        
        # 快速按钮
        result_btn_frame = tk.Frame(result_frame, bg='#f0f0f0')
        result_btn_frame.pack(fill='x', pady=5)
        
        self.result_var = tk.StringVar(value="1.0")
        
        results = [("+0.5%", "0.5"), ("+1.0%", "1.0"), ("+2.0%", "2.0"),
                  ("-0.5%", "-0.5"), ("-1.0%", "-1.0"), ("-2.0%", "-2.0")]
        
        for i, (text, value) in enumerate(results):
            color = '#27ae60' if '+' in text else '#e74c3c'
            btn = tk.Button(
                result_btn_frame,
                text=text,
                command=lambda v=value: self.set_result(v),
                bg=color,
                fg='white',
                font=('Arial', 9),
                width=8
            )
            row, col = i // 3, i % 3
            btn.grid(row=row, column=col, padx=2, pady=2)
        
        # 自定义结果输入
        result_input_frame = tk.Frame(result_frame, bg='#f0f0f0')
        result_input_frame.pack(fill='x', pady=5)
        
        tk.Label(result_input_frame, text="自定义:", bg='#f0f0f0').pack(side='left')
        self.result_entry = tk.Entry(result_input_frame, textvariable=self.result_var, width=10)
        self.result_entry.pack(side='left', padx=5)
        self.result_entry.bind('<KeyRelease>', self.on_input_change)
        tk.Label(result_input_frame, text="%", bg='#f0f0f0').pack(side='left')
        
        # 手续费率
        fee_frame = tk.LabelFrame(parent, text="💸 手续费率", bg='#f0f0f0')
        fee_frame.pack(fill='x', pady=5, padx=10)
        
        self.entry_fee_var = tk.StringVar(value="0.05")
        self.exit_fee_var = tk.StringVar(value="0.05")
        
        # 开仓手续费
        entry_fee_frame = tk.Frame(fee_frame, bg='#f0f0f0')
        entry_fee_frame.pack(fill='x', pady=2)
        
        tk.Label(entry_fee_frame, text="开仓费率:", bg='#f0f0f0', width=10).pack(side='left')
        self.entry_fee_entry = tk.Entry(entry_fee_frame, textvariable=self.entry_fee_var, width=10)
        self.entry_fee_entry.pack(side='left', padx=5)
        self.entry_fee_entry.bind('<KeyRelease>', self.on_input_change)
        tk.Label(entry_fee_frame, text="%", bg='#f0f0f0').pack(side='left')
        
        # 平仓手续费
        exit_fee_frame = tk.Frame(fee_frame, bg='#f0f0f0')
        exit_fee_frame.pack(fill='x', pady=2)
        
        tk.Label(exit_fee_frame, text="平仓费率:", bg='#f0f0f0', width=10).pack(side='left')
        self.exit_fee_entry = tk.Entry(exit_fee_frame, textvariable=self.exit_fee_var, width=10)
        self.exit_fee_entry.pack(side='left', padx=5)
        self.exit_fee_entry.bind('<KeyRelease>', self.on_input_change)
        tk.Label(exit_fee_frame, text="%", bg='#f0f0f0').pack(side='left')
        
        # 操作按钮
        button_frame = tk.Frame(parent, bg='#f0f0f0')
        button_frame.pack(fill='x', pady=10, padx=10)
        
        save_btn = tk.Button(
            button_frame,
            text="💾 保存交易记录",
            command=self.save_trade,
            bg='#27ae60',
            fg='white',
            font=('Arial', 12, 'bold'),
            height=2
        )
        save_btn.pack(fill='x', pady=2)
        
        clear_btn = tk.Button(
            button_frame,
            text="🗑️ 清空表单",
            command=self.clear_form,
            bg='#e74c3c',
            fg='white',
            font=('Arial', 10)
        )
        clear_btn.pack(fill='x', pady=2)
    
    def create_preview_section(self, parent):
        """创建预览区域"""
        preview_frame = tk.LabelFrame(
            parent,
            text="💰 实时计算预览",
            font=('Arial', 12, 'bold'),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        preview_frame.pack(fill='x', pady=5)
        
        # 计算结果显示
        self.calc_labels = {}
        
        calc_items = [
            ("gross_pnl", "毛收益:", "+100.00 元"),
            ("entry_fees", "开仓费用:", "-5.00 元"),
            ("exit_fees", "平仓费用:", "-5.00 元"),
            ("total_fees", "总手续费:", "-10.00 元"),
            ("net_pnl", "净收益:", "+90.00 元"),
            ("return_rate", "净收益率:", "+0.90%")
        ]
        
        for key, label_text, default_value in calc_items:
            frame = tk.Frame(preview_frame, bg='#f0f0f0')
            frame.pack(fill='x', pady=3, padx=10)
            
            tk.Label(frame, text=label_text, width=12, bg='#f0f0f0').pack(side='left')
            
            value_label = tk.Label(
                frame,
                text=default_value,
                font=('Arial', 11, 'bold'),
                bg='#f0f0f0',
                fg='green' if '+' in default_value else 'red' if '-' in default_value else 'blue'
            )
            value_label.pack(side='left', padx=10)
            
            self.calc_labels[key] = value_label
    
    def create_stats_section(self, parent):
        """创建统计区域"""
        stats_frame = tk.LabelFrame(
            parent,
            text="📈 数据库统计",
            font=('Arial', 12, 'bold'),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        stats_frame.pack(fill='both', expand=True, pady=5)
        
        # 统计信息
        self.stats_text = tk.Text(stats_frame, height=8, width=40, bg='white')
        scrollbar = tk.Scrollbar(stats_frame, orient='vertical', command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=scrollbar.set)
        
        self.stats_text.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar.pack(side='right', fill='y')
        
        # 刷新按钮
        refresh_btn = tk.Button(
            stats_frame,
            text="🔄 刷新统计",
            command=self.refresh_stats,
            bg='#3498db',
            fg='white'
        )
        refresh_btn.pack(pady=5)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_var = tk.StringVar(value="✅ 应用程序已就绪")
        status_bar = tk.Label(
            self.root,
            textvariable=self.status_var,
            relief='sunken',
            anchor='w',
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        status_bar.pack(side='bottom', fill='x')
    
    def set_position(self, value):
        """设置仓位大小"""
        self.position_var.set(value)
        self.on_input_change()
    
    def set_result(self, value):
        """设置交易结果"""
        self.result_var.set(value)
        self.on_input_change()
    
    def on_input_change(self, event=None):
        """输入变化处理"""
        # 延迟执行计算
        if hasattr(self, '_calc_timer'):
            self.root.after_cancel(self._calc_timer)
        
        self._calc_timer = self.root.after(300, self.update_calculations)
    
    def update_calculations(self):
        """更新计算结果"""
        try:
            # 获取输入值
            position_size = float(self.position_var.get() or 0)
            result_pct = float(self.result_var.get() or 0)
            entry_fee_pct = float(self.entry_fee_var.get() or 0)
            exit_fee_pct = float(self.exit_fee_var.get() or 0)
            
            # 计算指标
            gross_pnl = position_size * result_pct / 100
            entry_fees = position_size * entry_fee_pct / 100
            exit_fees = position_size * exit_fee_pct / 100
            total_fees = entry_fees + exit_fees
            net_pnl = gross_pnl - total_fees
            return_rate = (net_pnl / position_size * 100) if position_size > 0 else 0
            
            # 更新显示
            self.calc_labels['gross_pnl'].config(
                text=f"{gross_pnl:+.2f} 元",
                fg='green' if gross_pnl >= 0 else 'red'
            )
            
            self.calc_labels['entry_fees'].config(text=f"-{entry_fees:.2f} 元", fg='red')
            self.calc_labels['exit_fees'].config(text=f"-{exit_fees:.2f} 元", fg='red')
            self.calc_labels['total_fees'].config(text=f"-{total_fees:.2f} 元", fg='red')
            
            self.calc_labels['net_pnl'].config(
                text=f"{net_pnl:+.2f} 元",
                fg='green' if net_pnl >= 0 else 'red'
            )
            
            self.calc_labels['return_rate'].config(
                text=f"{return_rate:+.2f}%",
                fg='green' if return_rate >= 0 else 'red'
            )
            
        except ValueError:
            # 输入错误时显示默认值
            for key in self.calc_labels:
                self.calc_labels[key].config(text="--", fg='gray')
    
    def save_trade(self):
        """保存交易记录"""
        try:
            # 验证输入
            position_size = float(self.position_var.get())
            result_pct = float(self.result_var.get())
            entry_fee_pct = float(self.entry_fee_var.get())
            exit_fee_pct = float(self.exit_fee_var.get())
            
            if position_size <= 0:
                messagebox.showerror("输入错误", "仓位大小必须大于0")
                return
            
            # 计算指标
            gross_pnl = position_size * result_pct / 100
            entry_fees = position_size * entry_fee_pct / 100
            exit_fees = position_size * exit_fee_pct / 100
            total_fees = entry_fees + exit_fees
            net_pnl = gross_pnl - total_fees
            
            # 保存到数据库
            now = datetime.now()
            entry_time = (now - timedelta(hours=1)).strftime("%Y-%m-%d %H:%M")
            exit_time = now.strftime("%Y-%m-%d %H:%M")
            
            self.conn.execute("""
                INSERT INTO trades (
                    position_size, direction, result_pct, entry_time, exit_time,
                    entry_fee_pct, exit_fee_pct, instrument, notes,
                    gross_pnl, entry_fees, exit_fees, total_fees, net_pnl, duration_minutes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                position_size, self.direction_var.get(), result_pct,
                entry_time, exit_time, entry_fee_pct, exit_fee_pct,
                "BTC/USDT", "", gross_pnl, entry_fees, exit_fees,
                total_fees, net_pnl, 60
            ))
            
            self.conn.commit()
            
            self.status_var.set("✅ 交易记录保存成功！")
            self.refresh_stats()
            messagebox.showinfo("成功", "交易记录已保存！")
            
        except ValueError:
            messagebox.showerror("输入错误", "请检查数值格式")
        except Exception as e:
            messagebox.showerror("错误", f"保存失败：{str(e)}")
    
    def clear_form(self):
        """清空表单"""
        self.position_var.set("10000")
        self.direction_var.set("Long")
        self.result_var.set("1.0")
        self.entry_fee_var.set("0.05")
        self.exit_fee_var.set("0.05")
        self.on_input_change()
        self.status_var.set("🗑️ 表单已清空")
    
    def refresh_stats(self):
        """刷新统计信息"""
        try:
            cursor = self.conn.execute("""
                SELECT 
                    COUNT(*) as total_trades,
                    SUM(CASE WHEN net_pnl > 0 THEN 1 ELSE 0 END) as winning_trades,
                    SUM(net_pnl) as total_pnl,
                    AVG(net_pnl) as avg_pnl,
                    MAX(net_pnl) as max_profit,
                    MIN(net_pnl) as max_loss
                FROM trades
            """)
            
            stats = cursor.fetchone()
            
            self.stats_text.delete(1.0, tk.END)
            
            if stats['total_trades'] == 0:
                self.stats_text.insert(tk.END, "📭 暂无交易数据\n\n")
                self.stats_text.insert(tk.END, "请添加交易记录开始分析")
            else:
                win_rate = (stats['winning_trades'] / stats['total_trades']) * 100
                
                self.stats_text.insert(tk.END, "📊 交易统计概览\n")
                self.stats_text.insert(tk.END, "=" * 25 + "\n\n")
                self.stats_text.insert(tk.END, f"📈 总交易次数: {stats['total_trades']} 笔\n")
                self.stats_text.insert(tk.END, f"🎯 胜率: {win_rate:.1f}%\n")
                self.stats_text.insert(tk.END, f"💰 总盈亏: {stats['total_pnl']:+.2f} 元\n")
                self.stats_text.insert(tk.END, f"📊 平均盈亏: {stats['avg_pnl']:+.2f} 元\n")
                self.stats_text.insert(tk.END, f"🚀 最大盈利: {stats['max_profit']:+.2f} 元\n")
                self.stats_text.insert(tk.END, f"📉 最大亏损: {stats['max_loss']:+.2f} 元\n")
            
        except Exception as e:
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(tk.END, f"❌ 统计加载失败: {e}")
    
    def init_default_values(self):
        """初始化默认值"""
        self.on_input_change()
        self.refresh_stats()
    
    def run(self):
        """运行应用程序"""
        try:
            print("🚀 启动GUI主循环...")
            
            # 确保窗口正确显示
            self.root.after(100, lambda: self.root.lift())
            
            # 启动主循环
            self.root.mainloop()
            
        except Exception as e:
            print(f"❌ 应用程序运行失败: {e}")
        finally:
            # 清理资源
            try:
                self.conn.close()
                print("📝 数据库连接已关闭")
            except:
                pass
            print("🔚 应用程序已退出")

def main():
    """主函数"""
    try:
        app = TradingAppGUIFixed()
        app.run()
    except Exception as e:
        print(f"❌ 应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
