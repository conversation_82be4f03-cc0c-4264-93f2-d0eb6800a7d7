"""
进阶tkinter GUI测试程序
测试更多GUI组件和功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import platform

class GUITester:
    """GUI测试类"""
    
    def __init__(self):
        print("🔍 开始进阶GUI测试...")
        print(f"📋 Python版本: {sys.version}")
        print(f"💻 操作系统: {platform.system()} {platform.release()}")
        
        self.root = None
        self.test_results = []
        
    def test_tkinter_availability(self):
        """测试tkinter模块可用性"""
        try:
            print("1️⃣ 测试tkinter模块导入...")
            import tkinter
            print(f"✅ tkinter版本: {tkinter.TkVersion}")
            
            import tkinter.ttk
            print("✅ ttk模块导入成功")
            
            return True
        except ImportError as e:
            print(f"❌ tkinter导入失败: {e}")
            return False
    
    def create_test_window(self):
        """创建测试窗口"""
        try:
            print("2️⃣ 创建主窗口...")
            self.root = tk.Tk()
            self.root.title("🧪 GUI进阶测试")
            self.root.geometry("600x500")
            
            # 设置窗口居中
            self.root.update_idletasks()
            x = (self.root.winfo_screenwidth() // 2) - (600 // 2)
            y = (self.root.winfo_screenheight() // 2) - (500 // 2)
            self.root.geometry(f"600x500+{x}+{y}")
            
            print("✅ 主窗口创建成功")
            return True
            
        except Exception as e:
            print(f"❌ 主窗口创建失败: {e}")
            return False
    
    def test_basic_widgets(self):
        """测试基础组件"""
        try:
            print("3️⃣ 测试基础组件...")
            
            # 主标题
            title_label = tk.Label(
                self.root, 
                text="🧪 GUI组件测试", 
                font=("Arial", 18, "bold")
            )
            title_label.pack(pady=10)
            print("✅ 标题标签创建成功")
            
            # 创建框架
            main_frame = tk.Frame(self.root, relief="raised", bd=2)
            main_frame.pack(fill="both", expand=True, padx=20, pady=10)
            print("✅ 主框架创建成功")
            
            # 测试输入框
            input_frame = tk.LabelFrame(main_frame, text="📝 输入测试", padx=10, pady=10)
            input_frame.pack(fill="x", pady=5)
            
            tk.Label(input_frame, text="测试输入:").pack(anchor="w")
            self.test_entry = tk.Entry(input_frame, width=30)
            self.test_entry.pack(pady=5)
            self.test_entry.insert(0, "请输入测试文本...")
            print("✅ 输入框创建成功")
            
            # 测试按钮
            button_frame = tk.Frame(main_frame)
            button_frame.pack(fill="x", pady=10)
            
            test_btn = tk.Button(
                button_frame, 
                text="🧪 测试按钮", 
                command=self.on_test_button_click,
                bg="lightblue"
            )
            test_btn.pack(side="left", padx=5)
            
            clear_btn = tk.Button(
                button_frame, 
                text="🗑️ 清空", 
                command=self.clear_test,
                bg="lightcoral"
            )
            clear_btn.pack(side="left", padx=5)
            print("✅ 按钮创建成功")
            
            # 测试文本区域
            text_frame = tk.LabelFrame(main_frame, text="📄 输出区域", padx=10, pady=10)
            text_frame.pack(fill="both", expand=True, pady=5)
            
            self.output_text = tk.Text(text_frame, height=8, width=50)
            scrollbar = tk.Scrollbar(text_frame, orient="vertical", command=self.output_text.yview)
            self.output_text.configure(yscrollcommand=scrollbar.set)
            
            self.output_text.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")
            print("✅ 文本区域创建成功")
            
            # 初始化输出
            self.log_message("🎉 GUI组件测试程序启动成功！")
            self.log_message("📝 请测试各个组件的功能")
            
            return True
            
        except Exception as e:
            print(f"❌ 基础组件创建失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_ttk_widgets(self):
        """测试ttk组件"""
        try:
            print("4️⃣ 测试ttk组件...")
            
            ttk_frame = tk.LabelFrame(self.root, text="🎨 TTK组件测试", padx=10, pady=10)
            ttk_frame.pack(fill="x", padx=20, pady=5)
            
            # 进度条
            tk.Label(ttk_frame, text="进度条测试:").pack(anchor="w")
            self.progress = ttk.Progressbar(ttk_frame, length=200, mode='determinate')
            self.progress.pack(pady=5)
            
            # 下拉框
            tk.Label(ttk_frame, text="下拉框测试:").pack(anchor="w")
            self.combo = ttk.Combobox(ttk_frame, values=["选项1", "选项2", "选项3"])
            self.combo.pack(pady=5)
            self.combo.set("选择一个选项...")
            
            # TTK按钮
            ttk_btn = ttk.Button(
                ttk_frame, 
                text="🚀 TTK按钮测试", 
                command=self.test_ttk_features
            )
            ttk_btn.pack(pady=5)
            
            print("✅ TTK组件创建成功")
            return True
            
        except Exception as e:
            print(f"❌ TTK组件创建失败: {e}")
            return False
    
    def on_test_button_click(self):
        """测试按钮点击事件"""
        try:
            input_text = self.test_entry.get()
            self.log_message(f"🖱️ 按钮点击测试成功！")
            self.log_message(f"📝 输入内容: {input_text}")
            
            # 测试消息框
            messagebox.showinfo("测试成功", f"您输入的内容是: {input_text}")
            
        except Exception as e:
            self.log_message(f"❌ 按钮测试失败: {e}")
    
    def clear_test(self):
        """清空测试"""
        try:
            self.test_entry.delete(0, tk.END)
            self.output_text.delete(1.0, tk.END)
            self.log_message("🗑️ 清空操作完成")
        except Exception as e:
            print(f"❌ 清空操作失败: {e}")
    
    def test_ttk_features(self):
        """测试TTK功能"""
        try:
            # 进度条动画
            self.progress['value'] = 0
            self.animate_progress()
            
            selected = self.combo.get()
            self.log_message(f"🎨 TTK测试 - 选择项: {selected}")
            
        except Exception as e:
            self.log_message(f"❌ TTK测试失败: {e}")
    
    def animate_progress(self):
        """进度条动画"""
        try:
            current = self.progress['value']
            if current < 100:
                self.progress['value'] = current + 10
                self.root.after(100, self.animate_progress)
            else:
                self.log_message("✅ 进度条测试完成")
        except Exception as e:
            self.log_message(f"❌ 进度条动画失败: {e}")
    
    def log_message(self, message):
        """记录消息到输出区域"""
        try:
            if hasattr(self, 'output_text'):
                self.output_text.insert(tk.END, f"{message}\n")
                self.output_text.see(tk.END)
            print(message)
        except Exception as e:
            print(f"❌ 日志记录失败: {e}")
    
    def run_tests(self):
        """运行所有测试"""
        try:
            # 测试tkinter可用性
            if not self.test_tkinter_availability():
                return False
            
            # 创建窗口
            if not self.create_test_window():
                return False
            
            # 测试基础组件
            if not self.test_basic_widgets():
                return False
            
            # 测试TTK组件
            self.test_ttk_widgets()
            
            print("🚀 所有GUI组件创建完成，启动主循环...")
            print("💡 提示: 如果您能看到GUI窗口，说明tkinter工作正常！")
            
            # 强制窗口显示
            self.root.lift()
            self.root.attributes('-topmost', True)
            self.root.after(1000, lambda: self.root.attributes('-topmost', False))
            
            # 启动主循环
            self.root.mainloop()
            print("🔚 GUI测试程序已退出")
            return True
            
        except Exception as e:
            print(f"❌ GUI测试运行失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    print("=" * 50)
    print("🧪 GUI进阶测试程序")
    print("=" * 50)
    
    tester = GUITester()
    success = tester.run_tests()
    
    if success:
        print("✅ GUI测试完成")
    else:
        print("❌ GUI测试失败")
        print("💡 建议检查:")
        print("   1. Python版本是否支持tkinter")
        print("   2. 是否在支持GUI的环境中运行")
        print("   3. macOS是否需要安装额外的tkinter支持")

if __name__ == "__main__":
    main()
