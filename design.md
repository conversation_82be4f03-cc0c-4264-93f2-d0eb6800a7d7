Create a simple yet comprehensive trading review tool application with the following optimized specifications:

## Project Overview
Develop a local desktop trading review application to help traders efficiently record and analyze their completed trades. This is a post-trade review tool designed for quick data entry with intelligent defaults, automatic calculation of key trading metrics, and clear visualization of trading performance.

## Core Requirements

### 1. Simplified Data Input Module
**Required Fields (7 total):**
- Position size (numeric input with quick-select buttons and validation)
- Trade direction (radio buttons: Long/Short)
- Trade result percentage (percentage input, supports +/- values, e.g., +0.4%, -0.25%)
- Entry time (datetime picker, precise to minute)
- Exit time (datetime picker, precise to minute)
- Entry fee percentage (percentage input, default 0.05%)
- Exit fee percentage (percentage input, default 0.05%)

**Optional Fields:**
- Trading instrument/symbol (text input)
- Trading notes/reason (text area)

**Smart Default Value System:**
- Intelligent defaults based on recent usage patterns
- Remember last 3-5 used position sizes for quick selection
- Quick-select buttons for common trade results (+0.5%, +1%, +2%, -0.5%, -1%, -2%)
- Default fee rates: 0.05% for both entry and exit
- Template system for different trading styles
- All settings persist across sessions

**Real-time Calculation Preview:**
- Gross P&L = Position Size × Result Percentage
- Entry Fees = Position Size × Entry Fee Percentage
- Exit Fees = Position Size × Exit Fee Percentage
- Total Fees = Entry Fees + Exit Fees
- Net P&L = Gross P&L - Total Fees
- Trade Duration = Exit Time - Entry Time (displayed in minutes)
- Display live calculation as user inputs data

### 2. Enhanced Calculation Engine
**Per-Trade Calculations:**
- Gross profit/loss amount and percentage
- Total trading fees (entry + exit)
- Net profit/loss (after fees)
- Trade duration in minutes

**Daily Summary Calculations:**
- Daily total P&L and trade count
- Daily win rate and average profit/loss
- Daily fee summary
- Longest and shortest trade duration

**Period-based Statistical Analysis:**
- Customizable time period analysis (day/week/month/custom range)
- Overall win rate and separate long/short win rates
- Total and average P&L with breakdown
- Maximum profit and maximum loss trades
- Cumulative P&L curve and drawdown analysis
- Total fees paid and fee percentage of total volume
- Consecutive wins/losses streaks
- Trading frequency and patterns

### 3. Advanced Data Management
- CRUD operations for trading records
- Calendar-based daily performance visualization
- Daily trade aggregation and summary
- Flexible period filtering and analysis
- Multi-dimensional data grouping (by direction, date, performance)
- Data export to CSV format with custom date ranges
- Backup and restore functionality
- Historical performance comparison

## Technical Implementation

### Technology Stack
- **Language:** Python 3.8+
- **GUI Framework:** PyQt5/PySide2 (modern, responsive UI)
- **Database:** SQLite with optimized schema
- **Additional Libraries:** 
  - pandas for data manipulation
  - matplotlib for simple charts
  - datetime for time handling
  - csv for export functionality

### Simplified Architecture
Use streamlined MVC pattern:
- `models/trade.py`: Trade data model and calculations
- `views/main_window.py`: Main application window
- `views/trade_form.py`: Simplified trade input form
- `controllers/trade_controller.py`: Business logic
- `services/calculator.py`: P&L calculations
- `database/db_manager.py`: Database operations
- `utils/defaults.py`: Smart default value management

### Optimized Database Schema
```sql
-- Simplified trading records table
CREATE TABLE trades (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    position_size REAL NOT NULL,
    direction TEXT NOT NULL CHECK(direction IN ('Long', 'Short')),
    result_pct REAL NOT NULL,              -- Actual trade result (+/- percentage)
    entry_time DATETIME NOT NULL,
    exit_time DATETIME NOT NULL,
    entry_fee_pct REAL NOT NULL,           -- Entry fee percentage
    exit_fee_pct REAL NOT NULL,            -- Exit fee percentage
    instrument TEXT,
    notes TEXT,
    -- Calculated fields for performance
    gross_pnl REAL GENERATED ALWAYS AS (position_size * result_pct / 100),
    entry_fees REAL GENERATED ALWAYS AS (position_size * entry_fee_pct / 100),
    exit_fees REAL GENERATED ALWAYS AS (position_size * exit_fee_pct / 100),
    total_fees REAL GENERATED ALWAYS AS (entry_fees + exit_fees),
    net_pnl REAL GENERATED ALWAYS AS (gross_pnl - total_fees),
    duration_minutes REAL GENERATED ALWAYS AS ((julianday(exit_time) - julianday(entry_time)) * 1440),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Smart defaults and user preferences
CREATE TABLE user_preferences (
    key TEXT PRIMARY KEY,
    recent_values TEXT,                    -- JSON array of recently used values
    default_value TEXT,
    usage_count INTEGER DEFAULT 0
);

-- Trading templates for quick entry
CREATE TABLE trade_templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    position_size REAL,
    entry_fee_pct REAL,
    exit_fee_pct REAL,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### Streamlined UI Design
**Main Window Layout:**
- Clean tabbed interface with 3 main sections:
  1. "Quick Add" - Optimized trade input form
  2. "Trading Journal" - Calendar-based daily trading review
  3. "Data Analysis" - Comprehensive period-based analytics

**Optimized Trade Input Form:**
```
┌─────────────────────────────────────────┐
│  📊 Quick Trade Entry                    │
├─────────────────────────────────────────┤
│  Position Size                          │
│  [5K] [10K] [20K] [Custom: _____]      │
├─────────────────────────────────────────┤
│  Direction: ○ Long  ○ Short             │
├─────────────────────────────────────────┤  
│  Result:                                │
│  [+0.5%] [+1%] [+2%] [-0.5%] [-1%] [____%] │
├─────────────────────────────────────────┤
│  Entry Time: [2024-01-15 14:30] 📅      │
│  Exit Time:  [2024-01-15 15:45] 📅      │
│  Duration: 75 分钟                      │
├─────────────────────────────────────────┤
│  Entry Fee: [0.05%] [0.1%] [_____%]     │
│  Exit Fee:  [0.05%] [0.1%] [_____%]     │
├─────────────────────────────────────────┤
│  💰 Live Preview:                       │
│  毛收益: +100¥  手续费: -20¥  净收益: +80¥  │
├─────────────────────────────────────────┤
│  Notes: ________________________        │
│  [Save] [Save & New] [Use Template]     │
└─────────────────────────────────────────┘
```

**Enhanced Trading Journal Page:**
```
┌─────────────────────────────────────────┐
│  📅 Trading Calendar - 2024年1月        │
├─────────────────────────────────────────┤
│  日  一  二  三  四  五  六              │
│      1   2   3   4   5   6              │
│  7   8   9  10  11  12  13              │
│ 14  15 [16] 17  18  19  20              │ ← 当天高亮
│ 21  22  23  24  25  26  27              │   绿色=盈利日
│ 28  29  30  31                          │   红色=亏损日
├─────────────────────────────────────────┤
│  📊 2024-01-16 交易总结                  │
│  交易次数: 3笔 | 胜率: 66.7%             │
│  总盈亏: +¥285 | 手续费: ¥45            │
│  平均盈利: +¥190 | 平均亏损: -¥95       │
├─────────────────────────────────────────┤
│  📝 当日交易明细                         │
│  [交易列表 - 时间/方向/结果/盈亏]        │
└─────────────────────────────────────────┘
```

**Comprehensive Data Analysis Page:**
```
┌─────────────────────────────────────────┐
│  📈 Data Analysis Dashboard              │
├─────────────────────────────────────────┤
│  时间范围: [今日][本周][本月][自定义]     │
│  自定义: [2024-01-01] 至 [2024-01-31]   │
├─────────────────────────────────────────┤
│  🎯 核心指标                            │
│  总盈亏: +¥2,847  胜率: 65.2%           │
│  多单胜率: 70%    空单胜率: 58%         │
│  平均盈利: +¥186  平均亏损: -¥95        │
│  最大盈利: +¥450  最大亏损: -¥320       │
├─────────────────────────────────────────┤
│  📊 资金曲线图                          │
│  [显示累计盈亏变化趋势]                  │
├─────────────────────────────────────────┤
│  📋 详细统计                            │
│  交易次数: 23笔  手续费总额: ¥255       │
│  盈利交易: 15笔  亏损交易: 8笔          │
│  连胜记录: 5笔   连亏记录: 3笔          │
└─────────────────────────────────────────┘
```

**Enhanced User Experience:**
- One-click template selection
- Keyboard shortcuts (Ctrl+N: new trade, Ctrl+S: save, Tab: navigate)
- Auto-focus on most likely next field
- "Copy last trade" quick button
- Real-time input validation with helpful hints
- Confirmation only for data deletion

## Simplified Development Phases
1. **Foundation (Week 1):** Project setup, database, basic models
2. **Core Input (Week 2):** Smart trade input form with live calculations
3. **Data Management (Week 3):** Trade history, filtering, basic stats
4. **UI Polish (Week 4):** Templates, shortcuts, user experience improvements
5. **Final Features (Week 5):** Export, backup, simple visualizations

## Deliverables
- Lightweight Python desktop application
- SQLite database with intelligent schema
- Simple user guide with screenshots
- Clean, well-commented source code
- Requirements.txt with minimal dependencies

Build this as a fast, intuitive tool that makes trade recording effortless and provides clear insights into trading performance.