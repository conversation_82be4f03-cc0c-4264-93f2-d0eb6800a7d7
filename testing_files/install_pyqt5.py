"""
PyQt5安装和测试脚本
为交易复盘工具提供现代化GUI框架
"""

import subprocess
import sys
import os

def install_pyqt5():
    """安装PyQt5"""
    print("🔧 开始安装PyQt5...")
    
    try:
        # 检查是否已安装
        import PyQt5
        print("✅ PyQt5已安装")
        return True
    except ImportError:
        print("📦 PyQt5未安装，开始安装...")
        
        try:
            # 安装PyQt5
            subprocess.check_call([sys.executable, "-m", "pip", "install", "PyQt5"])
            print("✅ PyQt5安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ PyQt5安装失败: {e}")
            return False

def test_pyqt5():
    """测试PyQt5"""
    print("🧪 测试PyQt5...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QWidget, QLabel, QVBoxLayout, QPushButton
        from PyQt5.QtCore import Qt
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建窗口
        window = QWidget()
        window.setWindowTitle("PyQt5测试")
        window.setGeometry(300, 300, 400, 200)
        
        # 创建布局
        layout = QVBoxLayout()
        
        # 添加标签
        label = QLabel("🎉 PyQt5测试成功！")
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50;")
        layout.addWidget(label)
        
        # 添加按钮
        button = QPushButton("关闭测试")
        button.clicked.connect(window.close)
        button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px;
                font-size: 12px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        layout.addWidget(button)
        
        window.setLayout(layout)
        
        # 显示窗口
        window.show()
        
        print("✅ PyQt5测试窗口已显示")
        print("💡 如果您看到PyQt5测试窗口，说明PyQt5工作正常")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ PyQt5导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ PyQt5测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 PyQt5安装和测试工具")
    print("=" * 40)
    
    # 安装PyQt5
    if install_pyqt5():
        print("\n🧪 开始PyQt5测试...")
        test_pyqt5()
    else:
        print("\n❌ 无法安装PyQt5，请手动安装:")
        print("   pip install PyQt5")

if __name__ == "__main__":
    main()
