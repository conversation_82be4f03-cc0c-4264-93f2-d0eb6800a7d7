"""
交易复盘工具 - 现代化简化版本
基于HTML原型设计，实现现代化UI和优秀用户体验
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
from datetime import datetime, timedelta

class ModernTradingAppSimple:
    """现代化交易复盘工具GUI - 简化版本"""

    def __init__(self):
        print("🚀 启动现代化交易复盘工具")

        # 现代化配色方案
        self.colors = {
            'bg_primary': '#1a1a2e',      # 深蓝主背景
            'bg_secondary': '#16213e',    # 次要背景
            'bg_card': '#0f3460',         # 卡片背景
            'accent_blue': '#533483',     # 强调蓝色
            'accent_purple': '#7209b7',   # 强调紫色
            'success': '#2dd4bf',         # 成功色（青色）
            'danger': '#f87171',          # 危险色（红色）
            'warning': '#fbbf24',         # 警告色（黄色）
            'text_primary': '#f8fafc',    # 主文字
            'text_secondary': '#cbd5e1',  # 次要文字
            'text_muted': '#64748b',      # 弱化文字
        }

        # 初始化数据库
        self.init_database()

        # 创建主窗口
        self.create_main_window()

        # 创建现代化界面
        self.create_modern_ui()

        # 初始化数据
        self.init_data()

        print("✅ 现代化应用初始化完成")

    def init_database(self):
        """初始化数据库"""
        try:
            self.db_path = "trading_data.db"
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row

            # 创建表
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    position_size REAL NOT NULL,
                    direction TEXT NOT NULL,
                    result_pct REAL NOT NULL,
                    entry_time DATETIME NOT NULL,
                    exit_time DATETIME NOT NULL,
                    entry_fee_pct REAL NOT NULL DEFAULT 0.05,
                    exit_fee_pct REAL NOT NULL DEFAULT 0.05,
                    instrument TEXT DEFAULT '',
                    notes TEXT DEFAULT '',
                    gross_pnl REAL,
                    entry_fees REAL,
                    exit_fees REAL,
                    total_fees REAL,
                    net_pnl REAL,
                    duration_minutes REAL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            self.conn.commit()
            print("✅ 数据库初始化成功")

        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            raise

    def create_main_window(self):
        """创建主窗口"""
        try:
            # 设置环境变量
            os.environ['TK_SILENCE_DEPRECATION'] = '1'

            self.root = tk.Tk()
            self.root.title("📊 交易复盘工具 - 现代版")

            # 设置窗口大小和位置
            window_width = 1200
            window_height = 800

            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2

            self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
            self.root.minsize(1000, 700)

            # 设置现代化背景
            self.root.configure(bg=self.colors['bg_primary'])

            print("✅ 主窗口创建成功")

        except Exception as e:
            print(f"❌ 主窗口创建失败: {e}")
            raise

    def create_modern_ui(self):
        """创建现代化用户界面"""
        try:
            print("创建现代化界面...")

            # 主容器
            self.main_frame = tk.Frame(
                self.root,
                bg=self.colors['bg_primary'],
                padx=20,
                pady=20
            )
            self.main_frame.pack(fill='both', expand=True)

            # 创建标题区域
            self.create_header()

            # 创建主要内容区域
            self.create_content_area()

            # 创建状态栏
            self.create_status_bar()

            print("✅ 现代化界面创建成功")

        except Exception as e:
            print(f"❌ 现代化界面创建失败: {e}")
            raise

    def create_header(self):
        """创建标题区域"""
        # 标题容器
        header_frame = tk.Frame(
            self.main_frame,
            bg=self.colors['accent_purple'],
            height=80
        )
        header_frame.pack(fill='x', pady=(0, 20))
        header_frame.pack_propagate(False)

        # 主标题
        title_label = tk.Label(
            header_frame,
            text="📊 交易复盘工具 - 现代版",
            font=('Helvetica', 20, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['accent_purple']
        )
        title_label.pack(expand=True)

        # 副标题
        subtitle_label = tk.Label(
            header_frame,
            text="现代化设计 · 专业分析 · 智能复盘",
            font=('Helvetica', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['accent_purple']
        )
        subtitle_label.pack()

    def create_content_area(self):
        """创建主要内容区域"""
        # 内容容器
        content_frame = tk.Frame(
            self.main_frame,
            bg=self.colors['bg_primary']
        )
        content_frame.pack(fill='both', expand=True)

        # 左侧：输入区域
        left_frame = tk.Frame(
            content_frame,
            bg=self.colors['bg_secondary'],
            width=500
        )
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        left_frame.pack_propagate(False)

        # 右侧：预览区域
        right_frame = tk.Frame(
            content_frame,
            bg=self.colors['bg_secondary'],
            width=500
        )
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        right_frame.pack_propagate(False)

        # 创建左侧内容
        self.create_input_section(left_frame)

        # 创建右侧内容
        self.create_preview_section(right_frame)

    def create_input_section(self, parent):
        """创建输入区域"""
        # 输入区域标题
        input_title = tk.Label(
            parent,
            text="💰 交易信息录入",
            font=('Helvetica', 16, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        input_title.pack(pady=(20, 15))

        # 仓位大小区域
        self.create_position_section(parent)

        # 交易方向区域
        self.create_direction_section(parent)

        # 交易结果区域
        self.create_result_section(parent)

        # 手续费区域
        self.create_fees_section(parent)

        # 操作按钮区域
        self.create_action_buttons(parent)

    def create_position_section(self, parent):
        """创建仓位大小区域"""
        # 仓位区域容器
        pos_frame = tk.Frame(parent, bg=self.colors['bg_card'])
        pos_frame.pack(fill='x', padx=20, pady=10)

        # 标题
        tk.Label(
            pos_frame,
            text="💰 仓位大小",
            font=('Helvetica', 14, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_card']
        ).pack(pady=(15, 10))

        # 快速按钮
        button_frame = tk.Frame(pos_frame, bg=self.colors['bg_card'])
        button_frame.pack(pady=(0, 10))

        self.position_var = tk.StringVar(value="10000")
        positions = [("5千", "5000"), ("1万", "10000"), ("2万", "20000"), ("5万", "50000")]

        for text, value in positions:
            btn = tk.Button(
                button_frame,
                text=text,
                command=lambda v=value: self.set_position(v),
                font=('Helvetica', 10),
                fg=self.colors['text_primary'],
                bg=self.colors['accent_blue'],
                activebackground=self.colors['accent_purple'],
                relief='flat',
                padx=15,
                pady=5
            )
            btn.pack(side='left', padx=5)

        # 自定义输入
        input_frame = tk.Frame(pos_frame, bg=self.colors['bg_card'])
        input_frame.pack(pady=(0, 15))

        tk.Label(
            input_frame,
            text="自定义:",
            font=('Helvetica', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card']
        ).pack(side='left')

        self.position_entry = tk.Entry(
            input_frame,
            textvariable=self.position_var,
            font=('Helvetica', 12),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_primary'],
            width=15
        )
        self.position_entry.pack(side='left', padx=(10, 5))
        self.position_entry.bind('<KeyRelease>', self.on_input_change)

        tk.Label(
            input_frame,
            text="元",
            font=('Helvetica', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card']
        ).pack(side='left')

    def create_direction_section(self, parent):
        """创建交易方向区域"""
        # 方向区域容器
        dir_frame = tk.Frame(parent, bg=self.colors['bg_card'])
        dir_frame.pack(fill='x', padx=20, pady=10)

        # 标题
        tk.Label(
            dir_frame,
            text="📈 交易方向",
            font=('Helvetica', 14, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_card']
        ).pack(pady=(15, 10))

        # 方向选择
        direction_frame = tk.Frame(dir_frame, bg=self.colors['bg_card'])
        direction_frame.pack(pady=(0, 15))

        self.direction_var = tk.StringVar(value="Long")

        # 做多选项
        long_radio = tk.Radiobutton(
            direction_frame,
            text="📈 做多 (Long)",
            variable=self.direction_var,
            value="Long",
            command=self.on_input_change,
            font=('Helvetica', 12),
            fg=self.colors['success'],
            bg=self.colors['bg_card'],
            selectcolor=self.colors['bg_primary']
        )
        long_radio.pack(side='left', padx=(20, 30))

        # 做空选项
        short_radio = tk.Radiobutton(
            direction_frame,
            text="📉 做空 (Short)",
            variable=self.direction_var,
            value="Short",
            command=self.on_input_change,
            font=('Helvetica', 12),
            fg=self.colors['danger'],
            bg=self.colors['bg_card'],
            selectcolor=self.colors['bg_primary']
        )
        short_radio.pack(side='left')

    def create_result_section(self, parent):
        """创建交易结果区域"""
        # 结果区域容器
        result_frame = tk.Frame(parent, bg=self.colors['bg_card'])
        result_frame.pack(fill='x', padx=20, pady=10)

        # 标题
        tk.Label(
            result_frame,
            text="🎯 交易结果",
            font=('Helvetica', 14, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_card']
        ).pack(pady=(15, 10))

        # 快速按钮网格
        button_grid = tk.Frame(result_frame, bg=self.colors['bg_card'])
        button_grid.pack(pady=(0, 10))

        self.result_var = tk.StringVar(value="1.0")
        results = [("+0.5%", "0.5"), ("+1.0%", "1.0"), ("+2.0%", "2.0"),
                  ("-0.5%", "-0.5"), ("-1.0%", "-1.0"), ("-2.0%", "-2.0")]

        for i, (text, value) in enumerate(results):
            color = self.colors['success'] if '+' in text else self.colors['danger']
            btn = tk.Button(
                button_grid,
                text=text,
                command=lambda v=value: self.set_result(v),
                font=('Helvetica', 10),
                fg=color,
                bg=self.colors['bg_primary'],
                activebackground=color,
                activeforeground=self.colors['text_primary'],
                relief='flat',
                padx=12,
                pady=5
            )
            row, col = i // 3, i % 3
            btn.grid(row=row, column=col, padx=3, pady=3)

        # 自定义结果输入
        result_input_frame = tk.Frame(result_frame, bg=self.colors['bg_card'])
        result_input_frame.pack(pady=(0, 15))

        tk.Label(
            result_input_frame,
            text="自定义:",
            font=('Helvetica', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card']
        ).pack(side='left')

        self.result_entry = tk.Entry(
            result_input_frame,
            textvariable=self.result_var,
            font=('Helvetica', 12),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_primary'],
            width=10
        )
        self.result_entry.pack(side='left', padx=(10, 5))
        self.result_entry.bind('<KeyRelease>', self.on_input_change)

        tk.Label(
            result_input_frame,
            text="%",
            font=('Helvetica', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card']
        ).pack(side='left')

    def create_fees_section(self, parent):
        """创建手续费区域"""
        # 手续费区域容器
        fees_frame = tk.Frame(parent, bg=self.colors['bg_card'])
        fees_frame.pack(fill='x', padx=20, pady=10)

        # 标题
        tk.Label(
            fees_frame,
            text="💸 手续费率",
            font=('Helvetica', 14, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_card']
        ).pack(pady=(15, 10))

        self.entry_fee_var = tk.StringVar(value="0.05")
        self.exit_fee_var = tk.StringVar(value="0.05")

        # 开仓手续费
        entry_fee_frame = tk.Frame(fees_frame, bg=self.colors['bg_card'])
        entry_fee_frame.pack(pady=5)

        tk.Label(
            entry_fee_frame,
            text="开仓费率:",
            font=('Helvetica', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card'],
            width=10
        ).pack(side='left')

        self.entry_fee_entry = tk.Entry(
            entry_fee_frame,
            textvariable=self.entry_fee_var,
            font=('Helvetica', 12),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_primary'],
            width=10
        )
        self.entry_fee_entry.pack(side='left', padx=(10, 5))
        self.entry_fee_entry.bind('<KeyRelease>', self.on_input_change)

        tk.Label(
            entry_fee_frame,
            text="%",
            font=('Helvetica', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card']
        ).pack(side='left')

        # 平仓手续费
        exit_fee_frame = tk.Frame(fees_frame, bg=self.colors['bg_card'])
        exit_fee_frame.pack(pady=(5, 15))

        tk.Label(
            exit_fee_frame,
            text="平仓费率:",
            font=('Helvetica', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card'],
            width=10
        ).pack(side='left')

        self.exit_fee_entry = tk.Entry(
            exit_fee_frame,
            textvariable=self.exit_fee_var,
            font=('Helvetica', 12),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_primary'],
            width=10
        )
        self.exit_fee_entry.pack(side='left', padx=(10, 5))
        self.exit_fee_entry.bind('<KeyRelease>', self.on_input_change)

        tk.Label(
            exit_fee_frame,
            text="%",
            font=('Helvetica', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card']
        ).pack(side='left')

    def create_action_buttons(self, parent):
        """创建操作按钮"""
        # 按钮容器
        button_frame = tk.Frame(parent, bg=self.colors['bg_secondary'])
        button_frame.pack(fill='x', padx=20, pady=20)

        # 保存按钮
        save_btn = tk.Button(
            button_frame,
            text="💾 保存交易记录",
            command=self.save_trade,
            font=('Helvetica', 14, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['success'],
            activebackground=self.colors['accent_purple'],
            relief='flat',
            padx=30,
            pady=12
        )
        save_btn.pack(fill='x', pady=(0, 10))

        # 清空按钮
        clear_btn = tk.Button(
            button_frame,
            text="🗑️ 清空表单",
            command=self.clear_form,
            font=('Helvetica', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card'],
            activebackground=self.colors['danger'],
            activeforeground=self.colors['text_primary'],
            relief='flat',
            padx=20,
            pady=8
        )
        clear_btn.pack(fill='x')

    def create_preview_section(self, parent):
        """创建预览区域"""
        # 预览区域标题
        preview_title = tk.Label(
            parent,
            text="💰 实时计算预览",
            font=('Helvetica', 16, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        preview_title.pack(pady=(20, 15))

        # 计算预览容器
        preview_frame = tk.Frame(parent, bg=self.colors['accent_purple'])
        preview_frame.pack(fill='x', padx=20, pady=10)

        # 计算结果显示
        self.calc_labels = {}

        calc_items = [
            ("gross_pnl", "毛收益", "+100.00 元"),
            ("entry_fees", "开仓费用", "-5.00 元"),
            ("exit_fees", "平仓费用", "-5.00 元"),
            ("total_fees", "总手续费", "-10.00 元"),
            ("net_pnl", "净收益", "+90.00 元"),
            ("return_rate", "净收益率", "+0.90%")
        ]

        for key, label_text, default_value in calc_items:
            item_frame = tk.Frame(preview_frame, bg=self.colors['accent_purple'])
            item_frame.pack(fill='x', padx=15, pady=5)

            # 标签
            label = tk.Label(
                item_frame,
                text=label_text + ":",
                font=('Helvetica', 11),
                fg=self.colors['text_secondary'],
                bg=self.colors['accent_purple'],
                width=12,
                anchor='w'
            )
            label.pack(side='left', padx=(10, 5))

            # 数值
            value_label = tk.Label(
                item_frame,
                text=default_value,
                font=('Helvetica', 12, 'bold'),
                fg=self.colors['text_primary'],
                bg=self.colors['accent_purple'],
                anchor='w'
            )
            value_label.pack(side='left', padx=10)

            self.calc_labels[key] = value_label

        # 统计信息区域
        self.create_stats_section(parent)

    def create_stats_section(self, parent):
        """创建统计信息区域"""
        # 统计区域标题
        stats_title = tk.Label(
            parent,
            text="📈 数据库统计",
            font=('Helvetica', 16, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        stats_title.pack(pady=(30, 15))

        # 统计信息容器
        stats_frame = tk.Frame(parent, bg=self.colors['bg_card'])
        stats_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        # 统计文本区域
        self.stats_text = tk.Text(
            stats_frame,
            font=('Helvetica', 11),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_primary'],
            wrap='word',
            height=10
        )
        self.stats_text.pack(fill='both', expand=True, padx=15, pady=15)

        # 刷新按钮
        refresh_btn = tk.Button(
            stats_frame,
            text="🔄 刷新统计",
            command=self.refresh_stats,
            font=('Helvetica', 12),
            fg=self.colors['text_primary'],
            bg=self.colors['warning'],
            activebackground=self.colors['accent_purple'],
            relief='flat',
            padx=20,
            pady=8
        )
        refresh_btn.pack(pady=(0, 15))

    def create_status_bar(self):
        """创建状态栏"""
        self.status_var = tk.StringVar(value="✅ 现代化应用程序已就绪")
        status_bar = tk.Label(
            self.main_frame,
            textvariable=self.status_var,
            font=('Helvetica', 10),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card'],
            anchor='w',
            padx=20,
            pady=8
        )
        status_bar.pack(side='bottom', fill='x', pady=(10, 0))

    # 事件处理方法
    def set_position(self, value):
        """设置仓位大小"""
        self.position_var.set(value)
        self.on_input_change()

    def set_result(self, value):
        """设置交易结果"""
        self.result_var.set(value)
        self.on_input_change()

    def on_input_change(self, event=None):
        """输入变化处理"""
        # 延迟执行计算
        if hasattr(self, '_calc_timer'):
            self.root.after_cancel(self._calc_timer)

        self._calc_timer = self.root.after(300, self.update_calculations)

    def update_calculations(self):
        """更新计算结果"""
        try:
            # 获取输入值
            position_size = float(self.position_var.get() or 0)
            result_pct = float(self.result_var.get() or 0)
            entry_fee_pct = float(self.entry_fee_var.get() or 0)
            exit_fee_pct = float(self.exit_fee_var.get() or 0)

            # 计算指标
            gross_pnl = position_size * result_pct / 100
            entry_fees = position_size * entry_fee_pct / 100
            exit_fees = position_size * exit_fee_pct / 100
            total_fees = entry_fees + exit_fees
            net_pnl = gross_pnl - total_fees
            return_rate = (net_pnl / position_size * 100) if position_size > 0 else 0

            # 更新显示（使用现代化颜色）
            self.calc_labels['gross_pnl'].config(
                text=f"{gross_pnl:+.2f} 元",
                fg=self.colors['success'] if gross_pnl >= 0 else self.colors['danger']
            )

            self.calc_labels['entry_fees'].config(
                text=f"-{entry_fees:.2f} 元",
                fg=self.colors['danger']
            )

            self.calc_labels['exit_fees'].config(
                text=f"-{exit_fees:.2f} 元",
                fg=self.colors['danger']
            )

            self.calc_labels['total_fees'].config(
                text=f"-{total_fees:.2f} 元",
                fg=self.colors['danger']
            )

            self.calc_labels['net_pnl'].config(
                text=f"{net_pnl:+.2f} 元",
                fg=self.colors['success'] if net_pnl >= 0 else self.colors['danger']
            )

            self.calc_labels['return_rate'].config(
                text=f"{return_rate:+.2f}%",
                fg=self.colors['success'] if return_rate >= 0 else self.colors['danger']
            )

        except ValueError:
            # 输入错误时显示默认值
            for key in self.calc_labels:
                self.calc_labels[key].config(text="--", fg=self.colors['text_muted'])

    def save_trade(self):
        """保存交易记录"""
        try:
            # 验证输入
            position_size = float(self.position_var.get())
            result_pct = float(self.result_var.get())
            entry_fee_pct = float(self.entry_fee_var.get())
            exit_fee_pct = float(self.exit_fee_var.get())

            if position_size <= 0:
                messagebox.showerror("输入错误", "仓位大小必须大于0")
                return

            # 计算指标
            gross_pnl = position_size * result_pct / 100
            entry_fees = position_size * entry_fee_pct / 100
            exit_fees = position_size * exit_fee_pct / 100
            total_fees = entry_fees + exit_fees
            net_pnl = gross_pnl - total_fees

            # 保存到数据库
            now = datetime.now()
            entry_time = (now - timedelta(hours=1)).strftime("%Y-%m-%d %H:%M")
            exit_time = now.strftime("%Y-%m-%d %H:%M")

            self.conn.execute("""
                INSERT INTO trades (
                    position_size, direction, result_pct, entry_time, exit_time,
                    entry_fee_pct, exit_fee_pct, instrument, notes,
                    gross_pnl, entry_fees, exit_fees, total_fees, net_pnl, duration_minutes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                position_size, self.direction_var.get(), result_pct,
                entry_time, exit_time, entry_fee_pct, exit_fee_pct,
                "BTC/USDT", "", gross_pnl, entry_fees, exit_fees,
                total_fees, net_pnl, 60
            ))

            self.conn.commit()

            self.status_var.set("✅ 交易记录保存成功！")
            self.refresh_stats()
            messagebox.showinfo("保存成功", "🎉 交易记录已成功保存到数据库！")

        except ValueError:
            messagebox.showerror("输入错误", "请检查数值格式是否正确")
        except Exception as e:
            messagebox.showerror("保存错误", f"保存失败：{str(e)}")

    def clear_form(self):
        """清空表单"""
        self.position_var.set("10000")
        self.direction_var.set("Long")
        self.result_var.set("1.0")
        self.entry_fee_var.set("0.05")
        self.exit_fee_var.set("0.05")
        self.on_input_change()
        self.status_var.set("🗑️ 表单已清空")

    def refresh_stats(self):
        """刷新统计信息"""
        try:
            cursor = self.conn.execute("""
                SELECT
                    COUNT(*) as total_trades,
                    SUM(CASE WHEN net_pnl > 0 THEN 1 ELSE 0 END) as winning_trades,
                    SUM(net_pnl) as total_pnl,
                    AVG(net_pnl) as avg_pnl,
                    MAX(net_pnl) as max_profit,
                    MIN(net_pnl) as max_loss
                FROM trades
            """)

            stats = cursor.fetchone()

            self.stats_text.delete(1.0, tk.END)

            if stats['total_trades'] == 0:
                self.stats_text.insert(tk.END, "📭 暂无交易数据\n\n")
                self.stats_text.insert(tk.END, "请添加交易记录开始分析\n\n")
                self.stats_text.insert(tk.END, "💡 使用左侧表单快速录入交易信息")
            else:
                win_rate = (stats['winning_trades'] / stats['total_trades']) * 100

                # 使用现代化格式显示统计信息
                self.stats_text.insert(tk.END, "📊 交易统计概览\n")
                self.stats_text.insert(tk.END, "═" * 30 + "\n\n")

                self.stats_text.insert(tk.END, f"📈 总交易次数\n")
                self.stats_text.insert(tk.END, f"   {stats['total_trades']} 笔\n\n")

                self.stats_text.insert(tk.END, f"🎯 胜率统计\n")
                self.stats_text.insert(tk.END, f"   {win_rate:.1f}% ({stats['winning_trades']}/{stats['total_trades']})\n\n")

                self.stats_text.insert(tk.END, f"💰 盈亏概览\n")
                self.stats_text.insert(tk.END, f"   总盈亏: {stats['total_pnl']:+.2f} 元\n")
                self.stats_text.insert(tk.END, f"   平均盈亏: {stats['avg_pnl']:+.2f} 元\n\n")

                self.stats_text.insert(tk.END, f"📊 极值统计\n")
                self.stats_text.insert(tk.END, f"   最大盈利: {stats['max_profit']:+.2f} 元\n")
                self.stats_text.insert(tk.END, f"   最大亏损: {stats['max_loss']:+.2f} 元\n\n")

                # 添加性能评估
                if win_rate >= 70:
                    self.stats_text.insert(tk.END, "🌟 交易表现: 优秀\n")
                elif win_rate >= 60:
                    self.stats_text.insert(tk.END, "👍 交易表现: 良好\n")
                elif win_rate >= 50:
                    self.stats_text.insert(tk.END, "📈 交易表现: 一般\n")
                else:
                    self.stats_text.insert(tk.END, "⚠️ 交易表现: 需要改进\n")

        except Exception as e:
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(tk.END, f"❌ 统计加载失败: {e}")

    def init_data(self):
        """初始化数据"""
        self.on_input_change()
        self.refresh_stats()

    def run(self):
        """运行应用程序"""
        try:
            print("🚀 启动现代化GUI主循环...")

            # 确保窗口正确显示
            self.root.after(100, lambda: self.root.lift())

            # 启动主循环
            self.root.mainloop()

        except Exception as e:
            print(f"❌ 应用程序运行失败: {e}")
        finally:
            # 清理资源
            try:
                self.conn.close()
                print("📝 数据库连接已关闭")
            except:
                pass
            print("🔚 现代化应用程序已退出")

def main():
    """主函数"""
    try:
        app = ModernTradingAppSimple()
        app.run()
    except Exception as e:
        print(f"❌ 现代化应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
