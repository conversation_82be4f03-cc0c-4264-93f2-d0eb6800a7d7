"""
交易复盘工具 - 简化版本
专注于解决GUI显示问题的测试版本
"""

import tkinter as tk
from tkinter import messagebox
import sys
import platform
from datetime import datetime

class SimpleTradingApp:
    """简化版交易应用"""
    
    def __init__(self):
        print("🚀 启动简化版交易复盘工具...")
        print(f"📋 Python版本: {sys.version}")
        print(f"💻 操作系统: {platform.system()} {platform.release()}")
        
        self.root = None
        self.setup_window()
        self.create_simple_interface()
        
    def setup_window(self):
        """设置主窗口"""
        try:
            print("1️⃣ 创建主窗口...")
            self.root = tk.Tk()
            self.root.title("📊 交易复盘工具 - 简化版")
            self.root.geometry("500x400")
            
            # 窗口居中
            self.center_window()
            
            # 设置窗口属性
            self.root.resizable(True, True)
            
            print("✅ 主窗口创建成功")
            
        except Exception as e:
            print(f"❌ 主窗口创建失败: {e}")
            raise
    
    def center_window(self):
        """窗口居中显示"""
        try:
            self.root.update_idletasks()
            width = 500
            height = 400
            x = (self.root.winfo_screenwidth() // 2) - (width // 2)
            y = (self.root.winfo_screenheight() // 2) - (height // 2)
            self.root.geometry(f"{width}x{height}+{x}+{y}")
            print("✅ 窗口居中设置成功")
        except Exception as e:
            print(f"⚠️ 窗口居中失败: {e}")
    
    def create_simple_interface(self):
        """创建简化界面"""
        try:
            print("2️⃣ 创建界面组件...")
            
            # 主标题
            title_label = tk.Label(
                self.root, 
                text="📊 交易复盘工具", 
                font=("Arial", 16, "bold"),
                fg="blue"
            )
            title_label.pack(pady=10)
            print("✅ 标题创建成功")
            
            # 主框架
            main_frame = tk.Frame(self.root, relief="groove", bd=2)
            main_frame.pack(fill="both", expand=True, padx=20, pady=10)
            print("✅ 主框架创建成功")
            
            # 创建输入区域
            self.create_input_section(main_frame)
            
            # 创建按钮区域
            self.create_button_section(main_frame)
            
            # 创建输出区域
            self.create_output_section(main_frame)
            
            print("✅ 所有界面组件创建完成")
            
        except Exception as e:
            print(f"❌ 界面创建失败: {e}")
            import traceback
            traceback.print_exc()
            raise
    
    def create_input_section(self, parent):
        """创建输入区域"""
        try:
            print("3️⃣ 创建输入区域...")
            
            input_frame = tk.LabelFrame(parent, text="📝 交易信息", padx=10, pady=10)
            input_frame.pack(fill="x", pady=5)
            
            # 仓位大小
            tk.Label(input_frame, text="仓位大小:").grid(row=0, column=0, sticky="w", pady=2)
            self.position_var = tk.StringVar(value="10000")
            self.position_entry = tk.Entry(input_frame, textvariable=self.position_var, width=15)
            self.position_entry.grid(row=0, column=1, padx=5, pady=2)
            tk.Label(input_frame, text="元").grid(row=0, column=2, sticky="w")
            
            # 交易方向
            tk.Label(input_frame, text="交易方向:").grid(row=1, column=0, sticky="w", pady=2)
            self.direction_var = tk.StringVar(value="Long")
            direction_frame = tk.Frame(input_frame)
            direction_frame.grid(row=1, column=1, columnspan=2, sticky="w", pady=2)
            
            tk.Radiobutton(direction_frame, text="做多", variable=self.direction_var, value="Long").pack(side="left")
            tk.Radiobutton(direction_frame, text="做空", variable=self.direction_var, value="Short").pack(side="left", padx=10)
            
            # 交易结果
            tk.Label(input_frame, text="交易结果:").grid(row=2, column=0, sticky="w", pady=2)
            self.result_var = tk.StringVar(value="1.0")
            self.result_entry = tk.Entry(input_frame, textvariable=self.result_var, width=15)
            self.result_entry.grid(row=2, column=1, padx=5, pady=2)
            tk.Label(input_frame, text="%").grid(row=2, column=2, sticky="w")
            
            # 手续费率
            tk.Label(input_frame, text="手续费率:").grid(row=3, column=0, sticky="w", pady=2)
            self.fee_var = tk.StringVar(value="0.05")
            self.fee_entry = tk.Entry(input_frame, textvariable=self.fee_var, width=15)
            self.fee_entry.grid(row=3, column=1, padx=5, pady=2)
            tk.Label(input_frame, text="%").grid(row=3, column=2, sticky="w")
            
            print("✅ 输入区域创建成功")
            
        except Exception as e:
            print(f"❌ 输入区域创建失败: {e}")
            raise
    
    def create_button_section(self, parent):
        """创建按钮区域"""
        try:
            print("4️⃣ 创建按钮区域...")
            
            button_frame = tk.Frame(parent)
            button_frame.pack(fill="x", pady=10)
            
            # 计算按钮
            calc_btn = tk.Button(
                button_frame, 
                text="🧮 计算盈亏", 
                command=self.calculate_pnl,
                bg="lightgreen",
                width=12
            )
            calc_btn.pack(side="left", padx=5)
            
            # 清空按钮
            clear_btn = tk.Button(
                button_frame, 
                text="🗑️ 清空", 
                command=self.clear_form,
                bg="lightcoral",
                width=12
            )
            clear_btn.pack(side="left", padx=5)
            
            # 测试按钮
            test_btn = tk.Button(
                button_frame, 
                text="🧪 测试", 
                command=self.test_function,
                bg="lightblue",
                width=12
            )
            test_btn.pack(side="left", padx=5)
            
            print("✅ 按钮区域创建成功")
            
        except Exception as e:
            print(f"❌ 按钮区域创建失败: {e}")
            raise
    
    def create_output_section(self, parent):
        """创建输出区域"""
        try:
            print("5️⃣ 创建输出区域...")
            
            output_frame = tk.LabelFrame(parent, text="📊 计算结果", padx=10, pady=10)
            output_frame.pack(fill="both", expand=True, pady=5)
            
            # 结果显示
            self.result_text = tk.Text(output_frame, height=8, width=50)
            scrollbar = tk.Scrollbar(output_frame, orient="vertical", command=self.result_text.yview)
            self.result_text.configure(yscrollcommand=scrollbar.set)
            
            self.result_text.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")
            
            # 初始化显示
            self.log_result("🎉 简化版交易复盘工具启动成功！")
            self.log_result("📝 请输入交易信息并点击计算按钮")
            self.log_result(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            print("✅ 输出区域创建成功")
            
        except Exception as e:
            print(f"❌ 输出区域创建失败: {e}")
            raise
    
    def calculate_pnl(self):
        """计算盈亏"""
        try:
            print("🧮 开始计算盈亏...")
            
            # 获取输入值
            position_size = float(self.position_var.get() or 0)
            direction = self.direction_var.get()
            result_pct = float(self.result_var.get() or 0)
            fee_pct = float(self.fee_var.get() or 0)
            
            # 计算
            gross_pnl = position_size * result_pct / 100
            total_fees = position_size * fee_pct / 100 * 2  # 开仓+平仓
            net_pnl = gross_pnl - total_fees
            
            # 显示结果
            self.log_result("=" * 30)
            self.log_result(f"📊 计算结果 ({datetime.now().strftime('%H:%M:%S')})")
            self.log_result(f"💰 仓位大小: {position_size:,.2f} 元")
            self.log_result(f"📈 交易方向: {direction}")
            self.log_result(f"📊 交易结果: {result_pct:+.2f}%")
            self.log_result(f"💸 手续费率: {fee_pct:.2f}%")
            self.log_result("-" * 20)
            self.log_result(f"💵 毛盈亏: {gross_pnl:+,.2f} 元")
            self.log_result(f"💸 总手续费: -{total_fees:,.2f} 元")
            self.log_result(f"💰 净盈亏: {net_pnl:+,.2f} 元")
            self.log_result(f"📈 净收益率: {(net_pnl/position_size*100):+.2f}%")
            
            # 弹出结果
            result_msg = f"净盈亏: {net_pnl:+,.2f} 元\n净收益率: {(net_pnl/position_size*100):+.2f}%"
            messagebox.showinfo("计算结果", result_msg)
            
            print("✅ 盈亏计算完成")
            
        except ValueError as e:
            error_msg = "❌ 输入错误：请检查数值格式"
            self.log_result(error_msg)
            messagebox.showerror("输入错误", error_msg)
            print(f"❌ 输入错误: {e}")
            
        except Exception as e:
            error_msg = f"❌ 计算失败: {str(e)}"
            self.log_result(error_msg)
            messagebox.showerror("计算错误", error_msg)
            print(f"❌ 计算失败: {e}")
    
    def clear_form(self):
        """清空表单"""
        try:
            print("🗑️ 清空表单...")
            
            self.position_var.set("10000")
            self.direction_var.set("Long")
            self.result_var.set("1.0")
            self.fee_var.set("0.05")
            
            self.result_text.delete(1.0, tk.END)
            self.log_result("🗑️ 表单已清空")
            
            print("✅ 表单清空完成")
            
        except Exception as e:
            print(f"❌ 清空失败: {e}")
    
    def test_function(self):
        """测试功能"""
        try:
            print("🧪 执行测试功能...")
            
            self.log_result("🧪 测试功能执行中...")
            self.log_result(f"📱 GUI状态: 正常")
            self.log_result(f"🖱️ 按钮响应: 正常")
            self.log_result(f"📝 文本输出: 正常")
            
            messagebox.showinfo("测试结果", "🎉 所有功能测试通过！\nGUI工作正常")
            
            print("✅ 测试功能完成")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    def log_result(self, message):
        """记录结果到输出区域"""
        try:
            self.result_text.insert(tk.END, f"{message}\n")
            self.result_text.see(tk.END)
        except Exception as e:
            print(f"❌ 日志记录失败: {e}")
    
    def run(self):
        """运行应用程序"""
        try:
            print("🚀 启动GUI主循环...")
            print("💡 如果您能看到GUI窗口，说明应用程序工作正常！")
            
            # 强制窗口显示在前台
            self.root.lift()
            self.root.attributes('-topmost', True)
            self.root.after(1000, lambda: self.root.attributes('-topmost', False))
            
            # 启动主循环
            self.root.mainloop()
            print("🔚 应用程序已退出")
            
        except Exception as e:
            print(f"❌ 应用程序运行失败: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    print("=" * 50)
    print("📊 交易复盘工具 - 简化版")
    print("=" * 50)
    
    try:
        app = SimpleTradingApp()
        app.run()
    except Exception as e:
        print(f"❌ 应用程序启动失败: {e}")
        print("💡 可能的解决方案:")
        print("   1. 检查Python是否支持tkinter")
        print("   2. 在macOS上可能需要: brew install python-tk")
        print("   3. 确保在支持GUI的环境中运行")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
