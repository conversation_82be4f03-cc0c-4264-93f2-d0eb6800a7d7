"""
交易复盘工具 - 现代化GUI版本
基于HTML原型设计的现代化桌面应用
实现现代化UI设计和优秀用户体验
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List

class ModernTradingApp:
    """现代化交易复盘工具GUI"""

    def __init__(self):
        print("🚀 启动交易复盘工具 - 现代化版本")

        # 现代化配色方案（基于HTML原型）
        self.colors = {
            # 主色调
            'bg_primary': '#0f0f23',      # 主背景色
            'bg_secondary': '#1a1a2e',    # 次要背景色
            'bg_card': '#1e1e3f',         # 卡片背景色
            'bg_glass': '#2a2a4a',        # 玻璃态背景

            # 渐变色
            'primary_start': '#667eea',   # 主渐变起始色
            'primary_end': '#764ba2',     # 主渐变结束色
            'success_color': '#00ff88',   # 成功色（盈利）
            'danger_color': '#ff4757',    # 危险色（亏损）
            'neutral_color': '#74b9ff',   # 中性色

            # 文字颜色
            'text_primary': '#ffffff',    # 主文字色
            'text_secondary': '#a0a0a0',  # 次要文字色
            'text_muted': '#666666',      # 弱化文字色

            # 边框和阴影
            'border_color': '#3a3a5a',    # 边框色
            'shadow_color': '#000000',    # 阴影色
        }

        # 初始化数据库
        self.init_database()

        # 创建主窗口
        self.create_main_window()

        # 创建现代化界面
        self.create_modern_interface()

        # 初始化数据
        self.init_default_values()

        print("✅ 现代化GUI应用初始化完成")

    def init_database(self):
        """初始化数据库"""
        try:
            self.db_path = "trading_data.db"
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row

            # 创建表
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    position_size REAL NOT NULL,
                    direction TEXT NOT NULL,
                    result_pct REAL NOT NULL,
                    entry_time DATETIME NOT NULL,
                    exit_time DATETIME NOT NULL,
                    entry_fee_pct REAL NOT NULL DEFAULT 0.05,
                    exit_fee_pct REAL NOT NULL DEFAULT 0.05,
                    instrument TEXT DEFAULT '',
                    notes TEXT DEFAULT '',
                    gross_pnl REAL,
                    entry_fees REAL,
                    exit_fees REAL,
                    total_fees REAL,
                    net_pnl REAL,
                    duration_minutes REAL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            self.conn.commit()
            print("✅ 数据库初始化成功")

        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            raise

    def create_main_window(self):
        """创建主窗口"""
        try:
            # 设置环境变量消除警告
            os.environ['TK_SILENCE_DEPRECATION'] = '1'

            print("创建现代化主窗口...")
            self.root = tk.Tk()
            self.root.title("📊 交易复盘工具 - 现代版")

            # 设置窗口大小和位置
            window_width = 1200
            window_height = 800

            # 获取屏幕尺寸并居中
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2

            self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
            self.root.minsize(1000, 700)

            # 设置现代化背景色
            self.root.configure(bg=self.colors['bg_primary'])

            # 确保窗口正确显示
            self.root.update_idletasks()
            self.root.deiconify()

            print("✅ 现代化主窗口创建成功")

        except Exception as e:
            print(f"❌ 主窗口创建失败: {e}")
            raise

    def create_modern_interface(self):
        """创建现代化用户界面"""
        try:
            print("创建现代化用户界面...")

            # 创建主容器（模拟玻璃态效果）
            self.create_glass_container()

            # 创建现代化标题
            self.create_modern_header()

            # 创建主要内容区域
            self.create_main_content_modern()

            # 创建现代化状态栏
            self.create_modern_status_bar()

            print("✅ 现代化用户界面创建成功")

        except Exception as e:
            print(f"❌ 现代化用户界面创建失败: {e}")
            raise

    def create_glass_container(self):
        """创建玻璃态主容器"""
        # 主容器框架（模拟玻璃态效果）
        self.main_container = tk.Frame(
            self.root,
            bg=self.colors['bg_card'],
            relief='flat',
            bd=0
        )
        self.main_container.pack(fill='both', expand=True, padx=20, pady=20)

    def create_modern_header(self):
        """创建现代化标题区域"""
        # 标题容器（渐变效果模拟）
        header_frame = tk.Frame(
            self.main_container,
            bg=self.colors['primary_start'],
            height=100
        )
        header_frame.pack(fill='x', pady=(0, 20))
        header_frame.pack_propagate(False)

        # 主标题
        title_label = tk.Label(
            header_frame,
            text="📊 交易复盘工具",
            font=('Helvetica', 24, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['primary_start']
        )
        title_label.pack(expand=True, pady=(20, 5))

        # 副标题
        subtitle_label = tk.Label(
            header_frame,
            text="现代化设计 · 专业分析 · 智能复盘",
            font=('Helvetica', 14, 'normal'),
            fg='#ecf0f1',
            bg=self.colors['primary_start']
        )
        subtitle_label.pack(expand=True, pady=(0, 20))

    def create_main_content_modern(self):
        """创建现代化主要内容区域"""
        # 主内容框架
        content_frame = tk.Frame(
            self.main_container,
            bg=self.colors['bg_primary']
        )
        content_frame.pack(fill='both', expand=True)

        # 左侧：现代化输入区域
        left_panel = self.create_modern_left_panel(content_frame)
        left_panel.pack(side='left', fill='both', expand=True, padx=(0, 15))

        # 右侧：现代化预览区域
        right_panel = self.create_modern_right_panel(content_frame)
        right_panel.pack(side='right', fill='both', expand=True)

    def create_modern_left_panel(self, parent):
        """创建现代化左侧面板"""
        # 左侧主框架
        left_frame = tk.Frame(parent, bg=self.colors['bg_primary'])

        # 创建现代化输入卡片
        self.create_position_card(left_frame)
        self.create_direction_card(left_frame)
        self.create_result_card(left_frame)
        self.create_fees_card(left_frame)
        self.create_action_card(left_frame)

        return left_frame

    def create_position_card(self, parent):
        """创建仓位大小卡片"""
        # 卡片容器
        card_frame = tk.Frame(
            parent,
            bg=self.colors['bg_card'],
            relief='flat',
            bd=1
        )
        card_frame.pack(fill='x', pady=(0, 15), padx=5)

        # 卡片标题
        title_label = tk.Label(
            card_frame,
            text="💰 仓位大小",
            font=('Helvetica', 16, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_card']
        )
        title_label.pack(anchor='w', padx=20, pady=(20, 15))

        # 快速按钮容器
        button_container = tk.Frame(card_frame, bg=self.colors['bg_card'])
        button_container.pack(fill='x', padx=20, pady=(0, 15))

        # 快速按钮
        self.position_var = tk.StringVar(value="10000")
        positions = [("5千", "5000"), ("1万", "10000"), ("2万", "20000"), ("5万", "50000")]

        for i, (text, value) in enumerate(positions):
            btn = tk.Button(
                button_container,
                text=text,
                command=lambda v=value: self.set_position(v),
                font=('Helvetica', 12, 'normal'),
                fg=self.colors['text_secondary'],
                bg=self.colors['bg_glass'],
                activebackground=self.colors['primary_start'],
                activeforeground=self.colors['text_primary'],
                relief='flat',
                bd=0,
                padx=20,
                pady=10,
                cursor='hand2'
            )
            btn.pack(side='left', padx=(0, 10) if i < len(positions)-1 else 0)

        # 自定义输入
        input_frame = tk.Frame(card_frame, bg=self.colors['bg_card'])
        input_frame.pack(fill='x', padx=20, pady=(0, 20))

        tk.Label(
            input_frame,
            text="自定义:",
            font=('Helvetica', 12, 'normal'),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card']
        ).pack(side='left')

        self.position_entry = tk.Entry(
            input_frame,
            textvariable=self.position_var,
            font=('Helvetica', 12, 'normal'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_glass'],
            insertbackground=self.colors['text_primary'],
            relief='flat',
            bd=0,
            width=15
        )
        self.position_entry.pack(side='left', padx=(10, 5))
        self.position_entry.bind('<KeyRelease>', self.on_input_change)

        tk.Label(
            input_frame,
            text="元",
            font=('Helvetica', 12, 'normal'),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card']
        ).pack(side='left')

    def create_direction_card(self, parent):
        """创建交易方向卡片"""
        # 卡片容器
        card_frame = tk.Frame(
            parent,
            bg=self.colors['bg_card'],
            relief='flat',
            bd=1
        )
        card_frame.pack(fill='x', pady=(0, 15), padx=5)

        # 卡片标题
        title_label = tk.Label(
            card_frame,
            text="📈 交易方向",
            font=('SF Pro Display', 16, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_card']
        )
        title_label.pack(anchor='w', padx=20, pady=(20, 15))

        # 方向选择
        direction_frame = tk.Frame(card_frame, bg=self.colors['bg_card'])
        direction_frame.pack(fill='x', padx=20, pady=(0, 20))

        self.direction_var = tk.StringVar(value="Long")

        # 做多按钮
        long_btn = tk.Radiobutton(
            direction_frame,
            text="📈 做多 (Long)",
            variable=self.direction_var,
            value="Long",
            command=self.on_input_change,
            font=('SF Pro Display', 12),
            fg=self.colors['success_color'],
            bg=self.colors['bg_card'],
            selectcolor=self.colors['bg_glass'],
            activebackground=self.colors['bg_card'],
            activeforeground=self.colors['success_color'],
            cursor='hand2'
        )
        long_btn.pack(side='left', padx=(0, 30))

        # 做空按钮
        short_btn = tk.Radiobutton(
            direction_frame,
            text="📉 做空 (Short)",
            variable=self.direction_var,
            value="Short",
            command=self.on_input_change,
            font=('SF Pro Display', 12),
            fg=self.colors['danger_color'],
            bg=self.colors['bg_card'],
            selectcolor=self.colors['bg_glass'],
            activebackground=self.colors['bg_card'],
            activeforeground=self.colors['danger_color'],
            cursor='hand2'
        )
        short_btn.pack(side='left')

    def create_result_card(self, parent):
        """创建交易结果卡片"""
        # 卡片容器
        card_frame = tk.Frame(
            parent,
            bg=self.colors['bg_card'],
            relief='flat',
            bd=1
        )
        card_frame.pack(fill='x', pady=(0, 15), padx=5)

        # 卡片标题
        title_label = tk.Label(
            card_frame,
            text="🎯 交易结果",
            font=('SF Pro Display', 16, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_card']
        )
        title_label.pack(anchor='w', padx=20, pady=(20, 15))

        # 快速按钮网格
        button_grid = tk.Frame(card_frame, bg=self.colors['bg_card'])
        button_grid.pack(fill='x', padx=20, pady=(0, 15))

        self.result_var = tk.StringVar(value="1.0")
        results = [("+0.5%", "0.5"), ("+1.0%", "1.0"), ("+2.0%", "2.0"),
                  ("-0.5%", "-0.5"), ("-1.0%", "-1.0"), ("-2.0%", "-2.0")]

        for i, (text, value) in enumerate(results):
            color = self.colors['success_color'] if '+' in text else self.colors['danger_color']
            btn = tk.Button(
                button_grid,
                text=text,
                command=lambda v=value: self.set_result(v),
                font=('SF Pro Display', 11, '500'),
                fg=color,
                bg=self.colors['bg_glass'],
                activebackground=color,
                activeforeground=self.colors['text_primary'],
                relief='flat',
                bd=0,
                padx=15,
                pady=8,
                cursor='hand2'
            )
            row, col = i // 3, i % 3
            btn.grid(row=row, column=col, padx=5, pady=5, sticky='ew')

        # 配置网格权重
        for j in range(3):
            button_grid.columnconfigure(j, weight=1)

        # 自定义结果输入
        result_input_frame = tk.Frame(card_frame, bg=self.colors['bg_card'])
        result_input_frame.pack(fill='x', padx=20, pady=(0, 20))

        tk.Label(
            result_input_frame,
            text="自定义:",
            font=('SF Pro Display', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card']
        ).pack(side='left')

        self.result_entry = tk.Entry(
            result_input_frame,
            textvariable=self.result_var,
            font=('SF Pro Display', 12),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_glass'],
            insertbackground=self.colors['text_primary'],
            relief='flat',
            bd=0,
            width=10
        )
        self.result_entry.pack(side='left', padx=(10, 5))
        self.result_entry.bind('<KeyRelease>', self.on_input_change)

        tk.Label(
            result_input_frame,
            text="%",
            font=('SF Pro Display', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card']
        ).pack(side='left')

    def create_fees_card(self, parent):
        """创建手续费卡片"""
        # 卡片容器
        card_frame = tk.Frame(
            parent,
            bg=self.colors['bg_card'],
            relief='flat',
            bd=1
        )
        card_frame.pack(fill='x', pady=(0, 15), padx=5)

        # 卡片标题
        title_label = tk.Label(
            card_frame,
            text="💸 手续费率",
            font=('SF Pro Display', 16, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_card']
        )
        title_label.pack(anchor='w', padx=20, pady=(20, 15))

        self.entry_fee_var = tk.StringVar(value="0.05")
        self.exit_fee_var = tk.StringVar(value="0.05")

        # 开仓手续费
        entry_fee_frame = tk.Frame(card_frame, bg=self.colors['bg_card'])
        entry_fee_frame.pack(fill='x', padx=20, pady=(0, 10))

        tk.Label(
            entry_fee_frame,
            text="开仓费率:",
            font=('SF Pro Display', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card'],
            width=10
        ).pack(side='left')

        self.entry_fee_entry = tk.Entry(
            entry_fee_frame,
            textvariable=self.entry_fee_var,
            font=('SF Pro Display', 12),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_glass'],
            insertbackground=self.colors['text_primary'],
            relief='flat',
            bd=0,
            width=10
        )
        self.entry_fee_entry.pack(side='left', padx=(10, 5))
        self.entry_fee_entry.bind('<KeyRelease>', self.on_input_change)

        tk.Label(
            entry_fee_frame,
            text="%",
            font=('SF Pro Display', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card']
        ).pack(side='left')

        # 平仓手续费
        exit_fee_frame = tk.Frame(card_frame, bg=self.colors['bg_card'])
        exit_fee_frame.pack(fill='x', padx=20, pady=(0, 20))

        tk.Label(
            exit_fee_frame,
            text="平仓费率:",
            font=('SF Pro Display', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card'],
            width=10
        ).pack(side='left')

        self.exit_fee_entry = tk.Entry(
            exit_fee_frame,
            textvariable=self.exit_fee_var,
            font=('SF Pro Display', 12),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_glass'],
            insertbackground=self.colors['text_primary'],
            relief='flat',
            bd=0,
            width=10
        )
        self.exit_fee_entry.pack(side='left', padx=(10, 5))
        self.exit_fee_entry.bind('<KeyRelease>', self.on_input_change)

        tk.Label(
            exit_fee_frame,
            text="%",
            font=('SF Pro Display', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card']
        ).pack(side='left')

    def create_action_card(self, parent):
        """创建操作按钮卡片"""
        # 操作按钮容器
        action_frame = tk.Frame(parent, bg=self.colors['bg_primary'])
        action_frame.pack(fill='x', pady=20, padx=5)

        # 保存按钮（主要操作）
        save_btn = tk.Button(
            action_frame,
            text="💾 保存交易记录",
            command=self.save_trade,
            font=('SF Pro Display', 14, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['primary_start'],
            activebackground=self.colors['primary_end'],
            activeforeground=self.colors['text_primary'],
            relief='flat',
            bd=0,
            padx=30,
            pady=15,
            cursor='hand2'
        )
        save_btn.pack(fill='x', pady=(0, 10))

        # 清空按钮（次要操作）
        clear_btn = tk.Button(
            action_frame,
            text="🗑️ 清空表单",
            command=self.clear_form,
            font=('SF Pro Display', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_glass'],
            activebackground=self.colors['danger_color'],
            activeforeground=self.colors['text_primary'],
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        clear_btn.pack(fill='x')

    def create_modern_right_panel(self, parent):
        """创建现代化右侧面板"""
        # 右侧主框架
        right_frame = tk.Frame(parent, bg=self.colors['bg_primary'])

        # 创建实时预览卡片
        self.create_preview_card(right_frame)

        # 创建统计信息卡片
        self.create_stats_card(right_frame)

        return right_frame

    def create_preview_card(self, parent):
        """创建实时预览卡片"""
        # 预览卡片容器（渐变背景模拟）
        preview_frame = tk.Frame(
            parent,
            bg=self.colors['primary_start'],
            relief='flat',
            bd=0
        )
        preview_frame.pack(fill='x', pady=(0, 20), padx=5)

        # 预览标题
        title_label = tk.Label(
            preview_frame,
            text="💰 实时计算预览",
            font=('SF Pro Display', 18, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['primary_start']
        )
        title_label.pack(anchor='w', padx=25, pady=(25, 20))

        # 计算结果网格
        calc_grid = tk.Frame(preview_frame, bg=self.colors['primary_start'])
        calc_grid.pack(fill='x', padx=25, pady=(0, 25))

        # 创建计算结果显示
        self.calc_labels = {}

        calc_items = [
            ("gross_pnl", "毛收益", "+100.00 元"),
            ("entry_fees", "开仓费用", "-5.00 元"),
            ("exit_fees", "平仓费用", "-5.00 元"),
            ("total_fees", "总手续费", "-10.00 元"),
            ("net_pnl", "净收益", "+90.00 元"),
            ("return_rate", "净收益率", "+0.90%")
        ]

        for i, (key, label_text, default_value) in enumerate(calc_items):
            # 计算项容器
            item_frame = tk.Frame(
                calc_grid,
                bg='#5a6fd8',  # 稍微不同的背景色模拟玻璃效果
                relief='flat',
                bd=0
            )
            item_frame.pack(fill='x', pady=3)

            # 标签
            label = tk.Label(
                item_frame,
                text=label_text + ":",
                font=('SF Pro Display', 11),
                fg='#ecf0f1',
                bg='#5a6fd8',
                width=12
            )
            label.pack(side='left', padx=(15, 10), pady=8)

            # 数值
            value_label = tk.Label(
                item_frame,
                text=default_value,
                font=('SF Pro Display', 12, 'bold'),
                fg='white',
                bg='#5a6fd8'
            )
            value_label.pack(side='left', padx=10, pady=8)

            self.calc_labels[key] = value_label

    def create_stats_card(self, parent):
        """创建统计信息卡片"""
        # 统计卡片容器
        stats_frame = tk.Frame(
            parent,
            bg=self.colors['bg_card'],
            relief='flat',
            bd=1
        )
        stats_frame.pack(fill='both', expand=True, padx=5)

        # 统计标题
        title_label = tk.Label(
            stats_frame,
            text="📈 数据库统计",
            font=('SF Pro Display', 18, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_card']
        )
        title_label.pack(anchor='w', padx=25, pady=(25, 20))

        # 统计内容区域
        stats_content = tk.Frame(stats_frame, bg=self.colors['bg_card'])
        stats_content.pack(fill='both', expand=True, padx=25, pady=(0, 25))

        # 统计文本区域
        self.stats_text = tk.Text(
            stats_content,
            font=('SF Pro Display', 11),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_glass'],
            insertbackground=self.colors['text_primary'],
            relief='flat',
            bd=0,
            wrap='word',
            height=12
        )

        # 滚动条
        scrollbar = tk.Scrollbar(
            stats_content,
            orient='vertical',
            command=self.stats_text.yview,
            bg=self.colors['bg_glass'],
            troughcolor=self.colors['bg_card'],
            activebackground=self.colors['primary_start']
        )
        self.stats_text.configure(yscrollcommand=scrollbar.set)

        self.stats_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # 刷新按钮
        refresh_btn = tk.Button(
            stats_frame,
            text="🔄 刷新统计",
            command=self.refresh_stats,
            font=('SF Pro Display', 12, '500'),
            fg=self.colors['text_primary'],
            bg=self.colors['neutral_color'],
            activebackground=self.colors['primary_start'],
            activeforeground=self.colors['text_primary'],
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        refresh_btn.pack(pady=(0, 25))

    def create_modern_status_bar(self):
        """创建现代化状态栏"""
        self.status_var = tk.StringVar(value="✅ 现代化应用程序已就绪")
        status_bar = tk.Label(
            self.main_container,
            textvariable=self.status_var,
            font=('SF Pro Display', 10),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_secondary'],
            relief='flat',
            anchor='w',
            padx=20,
            pady=8
        )
        status_bar.pack(side='bottom', fill='x')

    # 事件处理方法
    def set_position(self, value):
        """设置仓位大小"""
        self.position_var.set(value)
        self.on_input_change()

    def set_result(self, value):
        """设置交易结果"""
        self.result_var.set(value)
        self.on_input_change()

    def on_input_change(self, event=None):
        """输入变化处理"""
        # 延迟执行计算
        if hasattr(self, '_calc_timer'):
            self.root.after_cancel(self._calc_timer)

        self._calc_timer = self.root.after(300, self.update_calculations)

    def update_calculations(self):
        """更新计算结果"""
        try:
            # 获取输入值
            position_size = float(self.position_var.get() or 0)
            result_pct = float(self.result_var.get() or 0)
            entry_fee_pct = float(self.entry_fee_var.get() or 0)
            exit_fee_pct = float(self.exit_fee_var.get() or 0)

            # 计算指标
            gross_pnl = position_size * result_pct / 100
            entry_fees = position_size * entry_fee_pct / 100
            exit_fees = position_size * exit_fee_pct / 100
            total_fees = entry_fees + exit_fees
            net_pnl = gross_pnl - total_fees
            return_rate = (net_pnl / position_size * 100) if position_size > 0 else 0

            # 更新显示（使用现代化颜色）
            self.calc_labels['gross_pnl'].config(
                text=f"{gross_pnl:+.2f} 元",
                fg=self.colors['success_color'] if gross_pnl >= 0 else self.colors['danger_color']
            )

            self.calc_labels['entry_fees'].config(
                text=f"-{entry_fees:.2f} 元",
                fg=self.colors['danger_color']
            )

            self.calc_labels['exit_fees'].config(
                text=f"-{exit_fees:.2f} 元",
                fg=self.colors['danger_color']
            )

            self.calc_labels['total_fees'].config(
                text=f"-{total_fees:.2f} 元",
                fg=self.colors['danger_color']
            )

            self.calc_labels['net_pnl'].config(
                text=f"{net_pnl:+.2f} 元",
                fg=self.colors['success_color'] if net_pnl >= 0 else self.colors['danger_color']
            )

            self.calc_labels['return_rate'].config(
                text=f"{return_rate:+.2f}%",
                fg=self.colors['success_color'] if return_rate >= 0 else self.colors['danger_color']
            )

        except ValueError:
            # 输入错误时显示默认值
            for key in self.calc_labels:
                self.calc_labels[key].config(text="--", fg=self.colors['text_muted'])

    def save_trade(self):
        """保存交易记录"""
        try:
            # 验证输入
            position_size = float(self.position_var.get())
            result_pct = float(self.result_var.get())
            entry_fee_pct = float(self.entry_fee_var.get())
            exit_fee_pct = float(self.exit_fee_var.get())

            if position_size <= 0:
                messagebox.showerror("输入错误", "仓位大小必须大于0")
                return

            # 计算指标
            gross_pnl = position_size * result_pct / 100
            entry_fees = position_size * entry_fee_pct / 100
            exit_fees = position_size * exit_fee_pct / 100
            total_fees = entry_fees + exit_fees
            net_pnl = gross_pnl - total_fees

            # 保存到数据库
            now = datetime.now()
            entry_time = (now - timedelta(hours=1)).strftime("%Y-%m-%d %H:%M")
            exit_time = now.strftime("%Y-%m-%d %H:%M")

            self.conn.execute("""
                INSERT INTO trades (
                    position_size, direction, result_pct, entry_time, exit_time,
                    entry_fee_pct, exit_fee_pct, instrument, notes,
                    gross_pnl, entry_fees, exit_fees, total_fees, net_pnl, duration_minutes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                position_size, self.direction_var.get(), result_pct,
                entry_time, exit_time, entry_fee_pct, exit_fee_pct,
                "BTC/USDT", "", gross_pnl, entry_fees, exit_fees,
                total_fees, net_pnl, 60
            ))

            self.conn.commit()

            self.status_var.set("✅ 交易记录保存成功！")
            self.refresh_stats()
            messagebox.showinfo("保存成功", "🎉 交易记录已成功保存到数据库！")

        except ValueError:
            messagebox.showerror("输入错误", "请检查数值格式是否正确")
        except Exception as e:
            messagebox.showerror("保存错误", f"保存失败：{str(e)}")

    def clear_form(self):
        """清空表单"""
        self.position_var.set("10000")
        self.direction_var.set("Long")
        self.result_var.set("1.0")
        self.entry_fee_var.set("0.05")
        self.exit_fee_var.set("0.05")
        self.on_input_change()
        self.status_var.set("🗑️ 表单已清空")

    def refresh_stats(self):
        """刷新统计信息"""
        try:
            cursor = self.conn.execute("""
                SELECT
                    COUNT(*) as total_trades,
                    SUM(CASE WHEN net_pnl > 0 THEN 1 ELSE 0 END) as winning_trades,
                    SUM(net_pnl) as total_pnl,
                    AVG(net_pnl) as avg_pnl,
                    MAX(net_pnl) as max_profit,
                    MIN(net_pnl) as max_loss
                FROM trades
            """)

            stats = cursor.fetchone()

            self.stats_text.delete(1.0, tk.END)

            if stats['total_trades'] == 0:
                self.stats_text.insert(tk.END, "📭 暂无交易数据\n\n")
                self.stats_text.insert(tk.END, "请添加交易记录开始分析\n\n")
                self.stats_text.insert(tk.END, "💡 使用左侧表单快速录入交易信息")
            else:
                win_rate = (stats['winning_trades'] / stats['total_trades']) * 100

                # 使用现代化格式显示统计信息
                self.stats_text.insert(tk.END, "📊 交易统计概览\n")
                self.stats_text.insert(tk.END, "═" * 30 + "\n\n")

                self.stats_text.insert(tk.END, f"📈 总交易次数\n")
                self.stats_text.insert(tk.END, f"   {stats['total_trades']} 笔\n\n")

                self.stats_text.insert(tk.END, f"🎯 胜率统计\n")
                self.stats_text.insert(tk.END, f"   {win_rate:.1f}% ({stats['winning_trades']}/{stats['total_trades']})\n\n")

                self.stats_text.insert(tk.END, f"💰 盈亏概览\n")
                self.stats_text.insert(tk.END, f"   总盈亏: {stats['total_pnl']:+.2f} 元\n")
                self.stats_text.insert(tk.END, f"   平均盈亏: {stats['avg_pnl']:+.2f} 元\n\n")

                self.stats_text.insert(tk.END, f"📊 极值统计\n")
                self.stats_text.insert(tk.END, f"   最大盈利: {stats['max_profit']:+.2f} 元\n")
                self.stats_text.insert(tk.END, f"   最大亏损: {stats['max_loss']:+.2f} 元\n\n")

                # 添加性能评估
                if win_rate >= 70:
                    self.stats_text.insert(tk.END, "🌟 交易表现: 优秀\n")
                elif win_rate >= 60:
                    self.stats_text.insert(tk.END, "👍 交易表现: 良好\n")
                elif win_rate >= 50:
                    self.stats_text.insert(tk.END, "📈 交易表现: 一般\n")
                else:
                    self.stats_text.insert(tk.END, "⚠️ 交易表现: 需要改进\n")

        except Exception as e:
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(tk.END, f"❌ 统计加载失败: {e}")

    def init_default_values(self):
        """初始化默认值"""
        self.on_input_change()
        self.refresh_stats()

    def run(self):
        """运行应用程序"""
        try:
            print("🚀 启动现代化GUI主循环...")

            # 确保窗口正确显示
            self.root.after(100, lambda: self.root.lift())

            # 启动主循环
            self.root.mainloop()

        except Exception as e:
            print(f"❌ 应用程序运行失败: {e}")
        finally:
            # 清理资源
            try:
                self.conn.close()
                print("📝 数据库连接已关闭")
            except:
                pass
            print("🔚 现代化应用程序已退出")

def main():
    """主函数"""
    try:
        app = ModernTradingApp()
        app.run()
    except Exception as e:
        print(f"❌ 现代化应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
