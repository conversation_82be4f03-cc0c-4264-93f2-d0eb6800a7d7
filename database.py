"""
交易复盘工具 - 数据库管理模块
负责SQLite数据库的创建、连接和基础操作
"""

import sqlite3
import os
from datetime import datetime
from typing import Optional, List, Dict, Any


class DatabaseManager:
    """数据库管理类 - 处理所有数据库操作"""
    
    def __init__(self, db_path: str = "trading_data.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.connection = None
        self.init_database()
    
    def init_database(self):
        """初始化数据库，创建必要的表结构"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # 使查询结果可以按列名访问
            
            # 创建交易记录表
            self.create_trades_table()
            
            # 创建用户配置表
            self.create_preferences_table()
            
            print(f"✅ 数据库初始化成功: {self.db_path}")
            
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            raise
    
    def create_trades_table(self):
        """创建交易记录表"""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS trades (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            position_size REAL NOT NULL CHECK(position_size > 0),
            direction TEXT NOT NULL CHECK(direction IN ('Long', 'Short')),
            result_pct REAL NOT NULL,
            entry_time DATETIME NOT NULL,
            exit_time DATETIME NOT NULL,
            entry_fee_pct REAL NOT NULL DEFAULT 0.05,
            exit_fee_pct REAL NOT NULL DEFAULT 0.05,
            instrument TEXT DEFAULT '',
            notes TEXT DEFAULT '',
            
            -- 计算字段
            gross_pnl REAL,
            entry_fees REAL,
            exit_fees REAL,
            total_fees REAL,
            net_pnl REAL,
            duration_minutes REAL,
            
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        self.connection.execute(create_table_sql)
        self.connection.commit()
    
    def create_preferences_table(self):
        """创建用户配置表"""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS user_preferences (
            key TEXT PRIMARY KEY,
            value TEXT,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        self.connection.execute(create_table_sql)
        self.connection.commit()
    
    def calculate_trade_metrics(self, position_size: float, result_pct: float, 
                              entry_fee_pct: float, exit_fee_pct: float,
                              entry_time: str, exit_time: str) -> Dict[str, float]:
        """
        计算交易指标
        
        Args:
            position_size: 仓位大小
            result_pct: 交易结果百分比
            entry_fee_pct: 开仓手续费率
            exit_fee_pct: 平仓手续费率
            entry_time: 开仓时间
            exit_time: 平仓时间
            
        Returns:
            包含所有计算指标的字典
        """
        # 基础计算
        gross_pnl = position_size * result_pct / 100
        entry_fees = position_size * entry_fee_pct / 100
        exit_fees = position_size * exit_fee_pct / 100
        total_fees = entry_fees + exit_fees
        net_pnl = gross_pnl - total_fees
        
        # 计算持仓时长（分钟）
        try:
            entry_dt = datetime.fromisoformat(entry_time.replace('T', ' '))
            exit_dt = datetime.fromisoformat(exit_time.replace('T', ' '))
            duration_minutes = (exit_dt - entry_dt).total_seconds() / 60
        except:
            duration_minutes = 0
        
        return {
            'gross_pnl': round(gross_pnl, 2),
            'entry_fees': round(entry_fees, 2),
            'exit_fees': round(exit_fees, 2),
            'total_fees': round(total_fees, 2),
            'net_pnl': round(net_pnl, 2),
            'duration_minutes': round(duration_minutes, 2)
        }
    
    def save_trade(self, trade_data: Dict[str, Any]) -> bool:
        """
        保存交易记录
        
        Args:
            trade_data: 交易数据字典
            
        Returns:
            保存是否成功
        """
        try:
            # 计算交易指标
            metrics = self.calculate_trade_metrics(
                trade_data['position_size'],
                trade_data['result_pct'],
                trade_data['entry_fee_pct'],
                trade_data['exit_fee_pct'],
                trade_data['entry_time'],
                trade_data['exit_time']
            )
            
            # 合并数据
            full_data = {**trade_data, **metrics}
            
            # 插入数据库
            insert_sql = """
            INSERT INTO trades (
                position_size, direction, result_pct, entry_time, exit_time,
                entry_fee_pct, exit_fee_pct, instrument, notes,
                gross_pnl, entry_fees, exit_fees, total_fees, net_pnl, duration_minutes
            ) VALUES (
                :position_size, :direction, :result_pct, :entry_time, :exit_time,
                :entry_fee_pct, :exit_fee_pct, :instrument, :notes,
                :gross_pnl, :entry_fees, :exit_fees, :total_fees, :net_pnl, :duration_minutes
            )
            """
            
            self.connection.execute(insert_sql, full_data)
            self.connection.commit()
            
            print("✅ 交易记录保存成功")
            return True
            
        except Exception as e:
            print(f"❌ 保存交易记录失败: {e}")
            return False
    
    def get_all_trades(self) -> List[Dict[str, Any]]:
        """获取所有交易记录"""
        try:
            cursor = self.connection.execute("""
                SELECT * FROM trades 
                ORDER BY entry_time DESC
            """)
            
            trades = []
            for row in cursor.fetchall():
                trades.append(dict(row))
            
            return trades
            
        except Exception as e:
            print(f"❌ 获取交易记录失败: {e}")
            return []
    
    def get_trade_count(self) -> int:
        """获取交易记录总数"""
        try:
            cursor = self.connection.execute("SELECT COUNT(*) FROM trades")
            return cursor.fetchone()[0]
        except:
            return 0
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("📝 数据库连接已关闭")


# 测试代码
if __name__ == "__main__":
    # 创建数据库管理器实例
    db = DatabaseManager("test_trading.db")
    
    # 测试数据
    test_trade = {
        'position_size': 10000,
        'direction': 'Long',
        'result_pct': 1.5,
        'entry_time': '2024-01-15 14:30:00',
        'exit_time': '2024-01-15 15:45:00',
        'entry_fee_pct': 0.05,
        'exit_fee_pct': 0.05,
        'instrument': 'BTC/USDT',
        'notes': '测试交易记录'
    }
    
    # 保存测试数据
    success = db.save_trade(test_trade)
    print(f"保存结果: {success}")
    
    # 获取所有记录
    trades = db.get_all_trades()
    print(f"总记录数: {len(trades)}")
    
    # 关闭数据库
    db.close()
