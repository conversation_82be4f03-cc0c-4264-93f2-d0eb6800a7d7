"""
GUI显示问题诊断工具
专门用于排查macOS上的tkinter显示问题
"""

import tkinter as tk
import sys
import os
import platform
import subprocess

def check_environment():
    """检查环境信息"""
    print("🔍 环境诊断开始...")
    print("=" * 50)
    
    # Python信息
    print(f"📋 Python版本: {sys.version}")
    print(f"📁 Python路径: {sys.executable}")
    
    # 系统信息
    print(f"💻 操作系统: {platform.system()} {platform.release()}")
    print(f"🏗️ 架构: {platform.machine()}")
    
    # 环境变量
    print(f"🖥️ DISPLAY变量: {os.environ.get('DISPLAY', '未设置')}")
    print(f"🎨 GUI环境: {os.environ.get('XDG_SESSION_TYPE', '未知')}")
    
    # tkinter信息
    try:
        import tkinter
        print(f"✅ tkinter版本: {tkinter.TkVersion}")
        print(f"📦 tcl版本: {tkinter.TclVersion}")
    except ImportError as e:
        print(f"❌ tkinter导入失败: {e}")
        return False
    
    return True

def test_display_capabilities():
    """测试显示能力"""
    print("\n🖥️ 显示能力测试...")
    print("-" * 30)
    
    try:
        # 检查是否在SSH会话中
        if 'SSH_CLIENT' in os.environ or 'SSH_TTY' in os.environ:
            print("⚠️ 检测到SSH会话 - 可能无法显示GUI")
            return False
        
        # 检查是否有显示器
        try:
            result = subprocess.run(['system_profiler', 'SPDisplaysDataType'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0 and 'Display' in result.stdout:
                print("✅ 检测到显示器")
            else:
                print("⚠️ 未检测到显示器信息")
        except:
            print("⚠️ 无法检查显示器状态")
        
        return True
        
    except Exception as e:
        print(f"❌ 显示能力检查失败: {e}")
        return False

def test_simple_window():
    """测试最简单的窗口"""
    print("\n🪟 简单窗口测试...")
    print("-" * 30)
    
    try:
        print("1️⃣ 创建根窗口...")
        root = tk.Tk()
        
        print("2️⃣ 设置窗口属性...")
        root.title("GUI测试")
        root.geometry("300x200")
        
        print("3️⃣ 强制窗口显示...")
        root.update()
        root.deiconify()
        root.lift()
        root.focus_force()
        
        # 尝试不同的置顶方法
        try:
            root.attributes('-topmost', True)
            print("✅ 设置窗口置顶")
        except:
            print("⚠️ 无法设置窗口置顶")
        
        print("4️⃣ 添加内容...")
        label = tk.Label(root, text="🎉 GUI测试成功！\n如果您看到这个窗口，\n说明GUI工作正常", 
                        font=("Arial", 12), justify="center")
        label.pack(expand=True)
        
        # 自动关闭按钮
        def close_window():
            print("✅ 用户确认看到窗口")
            root.destroy()
        
        button = tk.Button(root, text="我看到了，关闭窗口", command=close_window, 
                          bg="lightgreen", font=("Arial", 10))
        button.pack(pady=10)
        
        print("5️⃣ 启动主循环...")
        print("🚀 窗口应该已经显示，请查看屏幕！")
        
        # 设置自动关闭（10秒后）
        def auto_close():
            print("⏰ 10秒超时，自动关闭窗口")
            try:
                root.destroy()
            except:
                pass
        
        root.after(10000, auto_close)
        
        # 启动主循环
        root.mainloop()
        print("🔚 窗口已关闭")
        return True
        
    except Exception as e:
        print(f"❌ 简单窗口测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_window_positioning():
    """测试窗口定位"""
    print("\n📍 窗口定位测试...")
    print("-" * 30)
    
    try:
        root = tk.Tk()
        root.title("定位测试")
        
        # 获取屏幕尺寸
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        print(f"🖥️ 屏幕尺寸: {screen_width}x{screen_height}")
        
        # 设置窗口在屏幕中央
        window_width = 400
        window_height = 300
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        print(f"📍 窗口位置: {x},{y}")
        
        # 强制更新和显示
        root.update_idletasks()
        root.deiconify()
        root.lift()
        
        label = tk.Label(root, text="🎯 窗口定位测试\n窗口应该在屏幕中央", 
                        font=("Arial", 14), justify="center")
        label.pack(expand=True)
        
        # 3秒后自动关闭
        root.after(3000, root.destroy)
        root.mainloop()
        
        print("✅ 窗口定位测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 窗口定位测试失败: {e}")
        return False

def provide_solutions():
    """提供解决方案"""
    print("\n💡 可能的解决方案:")
    print("=" * 50)
    
    print("1️⃣ 如果您在macOS上遇到GUI显示问题:")
    print("   • 确保您不是通过SSH连接运行程序")
    print("   • 尝试安装更新的Python版本: brew install python@3.11")
    print("   • 安装tkinter支持: brew install python-tk")
    
    print("\n2️⃣ 如果看到tkinter弃用警告:")
    print("   • 这是正常的，不影响功能")
    print("   • 可以设置环境变量消除警告: export TK_SILENCE_DEPRECATION=1")
    
    print("\n3️⃣ 如果窗口创建成功但看不到:")
    print("   • 检查窗口是否被其他应用遮挡")
    print("   • 尝试使用Cmd+Tab切换应用")
    print("   • 检查Dock中是否有Python图标")
    
    print("\n4️⃣ 替代方案:")
    print("   • 考虑使用PyQt5/PySide2: pip install PyQt5")
    print("   • 或者使用web界面: Flask + HTML")
    print("   • 命令行版本也是一个选择")

def main():
    """主诊断函数"""
    print("🔧 GUI显示问题诊断工具")
    print("=" * 50)
    
    # 环境检查
    if not check_environment():
        print("❌ 环境检查失败")
        provide_solutions()
        return
    
    # 显示能力测试
    test_display_capabilities()
    
    # 简单窗口测试
    print("\n⚠️ 即将显示测试窗口，请注意观察屏幕...")
    input("按回车键继续...")
    
    if test_simple_window():
        print("✅ GUI测试成功！")
    else:
        print("❌ GUI测试失败")
        provide_solutions()
    
    # 窗口定位测试
    print("\n📍 进行窗口定位测试...")
    test_window_positioning()
    
    print("\n🎉 诊断完成！")

if __name__ == "__main__":
    main()
