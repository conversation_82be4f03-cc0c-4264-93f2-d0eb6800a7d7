"""
交易数据可视化模块
提供图表分析功能，支持控制台和GUI集成
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
import numpy as np
import tkinter as tk
from tkinter import ttk

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class TradingVisualizer:
    """交易数据可视化类"""
    
    def __init__(self, db_path="trading_data.db"):
        self.db_path = db_path
        self.conn = sqlite3.connect(db_path)
        
    def get_trade_data(self):
        """获取交易数据"""
        try:
            df = pd.read_sql_query("""
                SELECT * FROM trades 
                ORDER BY entry_time
            """, self.conn)
            
            if not df.empty:
                df['entry_time'] = pd.to_datetime(df['entry_time'])
                df['exit_time'] = pd.to_datetime(df['exit_time'])
                df['date'] = df['entry_time'].dt.date
            
            return df
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return pd.DataFrame()
    
    def create_pnl_chart(self, save_path=None, show=True):
        """创建盈亏图表"""
        df = self.get_trade_data()
        
        if df.empty:
            print("📭 暂无数据可视化")
            return None
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        fig.suptitle('📊 交易盈亏分析', fontsize=16, fontweight='bold')
        
        # 累计盈亏图
        df['cumulative_pnl'] = df['net_pnl'].cumsum()
        
        ax1.plot(df['entry_time'], df['cumulative_pnl'], 
                marker='o', linewidth=2, markersize=4, color='#2c3e50')
        ax1.fill_between(df['entry_time'], df['cumulative_pnl'], 
                        alpha=0.3, color='#3498db')
        ax1.set_title('📈 累计盈亏曲线', fontsize=14, fontweight='bold')
        ax1.set_ylabel('累计盈亏 (元)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=0, color='red', linestyle='--', alpha=0.7)
        
        # 格式化x轴日期
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax1.xaxis.set_major_locator(mdates.DayLocator(interval=1))
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
        
        # 单笔盈亏柱状图
        colors = ['#27ae60' if pnl >= 0 else '#e74c3c' for pnl in df['net_pnl']]
        bars = ax2.bar(range(len(df)), df['net_pnl'], color=colors, alpha=0.7)
        
        ax2.set_title('📊 单笔交易盈亏', fontsize=14, fontweight='bold')
        ax2.set_ylabel('单笔盈亏 (元)', fontsize=12)
        ax2.set_xlabel('交易序号', fontsize=12)
        ax2.grid(True, alpha=0.3, axis='y')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # 添加数值标签
        for i, (bar, pnl) in enumerate(zip(bars, df['net_pnl'])):
            if abs(pnl) > max(abs(df['net_pnl'])) * 0.1:  # 只显示较大的值
                ax2.text(bar.get_x() + bar.get_width()/2, 
                        bar.get_height() + (5 if pnl >= 0 else -15),
                        f'{pnl:.0f}', ha='center', va='bottom' if pnl >= 0 else 'top',
                        fontsize=8)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 图表已保存到: {save_path}")
        
        if show:
            plt.show()
        
        return fig
    
    def create_statistics_chart(self, save_path=None, show=True):
        """创建统计分析图表"""
        df = self.get_trade_data()
        
        if df.empty:
            print("📭 暂无数据可视化")
            return None
        
        # 创建2x2子图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('📈 交易统计分析', fontsize=16, fontweight='bold')
        
        # 1. 胜负分布饼图
        wins = len(df[df['net_pnl'] > 0])
        losses = len(df[df['net_pnl'] <= 0])
        
        if wins + losses > 0:
            labels = [f'盈利 ({wins}笔)', f'亏损 ({losses}笔)']
            sizes = [wins, losses]
            colors = ['#27ae60', '#e74c3c']
            
            ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%',
                   startangle=90, textprops={'fontsize': 10})
            ax1.set_title('🎯 胜负分布', fontsize=12, fontweight='bold')
        
        # 2. 方向分布
        direction_counts = df['direction'].value_counts()
        if not direction_counts.empty:
            ax2.bar(direction_counts.index, direction_counts.values, 
                   color=['#3498db', '#f39c12'], alpha=0.7)
            ax2.set_title('📊 交易方向分布', fontsize=12, fontweight='bold')
            ax2.set_ylabel('交易次数', fontsize=10)
            
            # 添加数值标签
            for i, v in enumerate(direction_counts.values):
                ax2.text(i, v + 0.1, str(v), ha='center', va='bottom')
        
        # 3. 盈亏分布直方图
        ax3.hist(df['net_pnl'], bins=20, color='#9b59b6', alpha=0.7, edgecolor='black')
        ax3.set_title('📈 盈亏分布直方图', fontsize=12, fontweight='bold')
        ax3.set_xlabel('净盈亏 (元)', fontsize=10)
        ax3.set_ylabel('频次', fontsize=10)
        ax3.axvline(x=0, color='red', linestyle='--', alpha=0.7)
        ax3.grid(True, alpha=0.3)
        
        # 4. 时间序列分析
        if len(df) > 1:
            # 按日期聚合
            daily_pnl = df.groupby('date')['net_pnl'].sum().reset_index()
            daily_pnl['date'] = pd.to_datetime(daily_pnl['date'])
            
            ax4.plot(daily_pnl['date'], daily_pnl['net_pnl'], 
                    marker='o', linewidth=2, markersize=6, color='#e67e22')
            ax4.set_title('📅 每日盈亏趋势', fontsize=12, fontweight='bold')
            ax4.set_ylabel('每日盈亏 (元)', fontsize=10)
            ax4.grid(True, alpha=0.3)
            ax4.axhline(y=0, color='red', linestyle='--', alpha=0.7)
            
            # 格式化日期
            ax4.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
            plt.setp(ax4.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 统计图表已保存到: {save_path}")
        
        if show:
            plt.show()
        
        return fig
    
    def create_calendar_heatmap(self, save_path=None, show=True):
        """创建日历热力图"""
        df = self.get_trade_data()
        
        if df.empty:
            print("📭 暂无数据可视化")
            return None
        
        # 按日期聚合数据
        daily_data = df.groupby('date').agg({
            'net_pnl': 'sum',
            'id': 'count'
        }).reset_index()
        daily_data.columns = ['date', 'daily_pnl', 'trade_count']
        
        # 创建日期范围
        start_date = daily_data['date'].min()
        end_date = daily_data['date'].max()
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        
        # 创建完整的日期数据框
        full_dates = pd.DataFrame({'date': date_range.date})
        merged_data = full_dates.merge(daily_data, on='date', how='left')
        merged_data['daily_pnl'] = merged_data['daily_pnl'].fillna(0)
        merged_data['trade_count'] = merged_data['trade_count'].fillna(0)
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(15, 8))
        
        # 计算周数和星期
        merged_data['date'] = pd.to_datetime(merged_data['date'])
        merged_data['week'] = merged_data['date'].dt.isocalendar().week
        merged_data['weekday'] = merged_data['date'].dt.dayofweek
        
        # 创建热力图数据
        weeks = sorted(merged_data['week'].unique())
        heatmap_data = np.zeros((7, len(weeks)))
        
        for i, week in enumerate(weeks):
            week_data = merged_data[merged_data['week'] == week]
            for _, row in week_data.iterrows():
                heatmap_data[row['weekday'], i] = row['daily_pnl']
        
        # 绘制热力图
        im = ax.imshow(heatmap_data, cmap='RdYlGn', aspect='auto')
        
        # 设置标签
        ax.set_title('📅 交易日历热力图', fontsize=16, fontweight='bold', pad=20)
        ax.set_ylabel('星期', fontsize=12)
        ax.set_xlabel('周数', fontsize=12)
        
        # 设置y轴标签
        weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        ax.set_yticks(range(7))
        ax.set_yticklabels(weekdays)
        
        # 设置x轴标签
        ax.set_xticks(range(0, len(weeks), max(1, len(weeks)//10)))
        ax.set_xticklabels([f'第{weeks[i]}周' for i in range(0, len(weeks), max(1, len(weeks)//10))])
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('每日盈亏 (元)', fontsize=12)
        
        # 添加数值标签
        for i in range(7):
            for j in range(len(weeks)):
                if heatmap_data[i, j] != 0:
                    text = ax.text(j, i, f'{heatmap_data[i, j]:.0f}',
                                 ha="center", va="center", color="black", fontsize=8)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📅 日历热力图已保存到: {save_path}")
        
        if show:
            plt.show()
        
        return fig
    
    def generate_all_charts(self, output_dir="charts"):
        """生成所有图表"""
        import os
        
        # 创建输出目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        print(f"📊 开始生成图表到目录: {output_dir}")
        
        # 生成各种图表
        charts = [
            ("盈亏分析", "pnl_analysis.png", self.create_pnl_chart),
            ("统计分析", "statistics.png", self.create_statistics_chart),
            ("日历热力图", "calendar_heatmap.png", self.create_calendar_heatmap)
        ]
        
        for name, filename, func in charts:
            try:
                print(f"生成 {name}...")
                save_path = os.path.join(output_dir, filename)
                func(save_path=save_path, show=False)
                print(f"✅ {name} 生成完成")
            except Exception as e:
                print(f"❌ {name} 生成失败: {e}")
        
        print(f"🎉 所有图表生成完成！请查看 {output_dir} 目录")
    
    def close(self):
        """关闭数据库连接"""
        self.conn.close()

class TradingVisualizationGUI:
    """交易可视化GUI界面"""
    
    def __init__(self, parent=None):
        self.visualizer = TradingVisualizer()
        
        if parent is None:
            self.root = tk.Tk()
            self.root.title("📊 交易数据可视化")
            self.root.geometry("800x600")
        else:
            self.root = tk.Toplevel(parent)
            self.root.title("📊 交易数据可视化")
            self.root.geometry("800x600")
        
        self.create_interface()
    
    def create_interface(self):
        """创建界面"""
        # 标题
        title_label = tk.Label(
            self.root,
            text="📊 交易数据可视化分析",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50'
        )
        title_label.pack(pady=10)
        
        # 按钮框架
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20)
        
        # 图表按钮
        buttons = [
            ("📈 盈亏分析图", self.show_pnl_chart, '#27ae60'),
            ("📊 统计分析图", self.show_statistics_chart, '#3498db'),
            ("📅 日历热力图", self.show_calendar_heatmap, '#e67e22'),
            ("💾 导出所有图表", self.export_all_charts, '#9b59b6')
        ]
        
        for text, command, color in buttons:
            btn = tk.Button(
                button_frame,
                text=text,
                command=command,
                bg=color,
                fg='white',
                font=('Arial', 12),
                width=20,
                height=2
            )
            btn.pack(pady=5)
        
        # 图表显示区域
        self.chart_frame = tk.Frame(self.root, relief='sunken', bd=2)
        self.chart_frame.pack(fill='both', expand=True, padx=20, pady=10)
    
    def show_pnl_chart(self):
        """显示盈亏图表"""
        self.clear_chart_frame()
        fig = self.visualizer.create_pnl_chart(show=False)
        if fig:
            self.embed_chart(fig)
    
    def show_statistics_chart(self):
        """显示统计图表"""
        self.clear_chart_frame()
        fig = self.visualizer.create_statistics_chart(show=False)
        if fig:
            self.embed_chart(fig)
    
    def show_calendar_heatmap(self):
        """显示日历热力图"""
        self.clear_chart_frame()
        fig = self.visualizer.create_calendar_heatmap(show=False)
        if fig:
            self.embed_chart(fig)
    
    def export_all_charts(self):
        """导出所有图表"""
        self.visualizer.generate_all_charts()
        tk.messagebox.showinfo("完成", "所有图表已导出到 charts 目录！")
    
    def clear_chart_frame(self):
        """清空图表区域"""
        for widget in self.chart_frame.winfo_children():
            widget.destroy()
    
    def embed_chart(self, fig):
        """嵌入图表到界面"""
        canvas = FigureCanvasTkAgg(fig, self.chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)
    
    def run(self):
        """运行界面"""
        self.root.mainloop()

# 测试代码
if __name__ == "__main__":
    print("📊 交易数据可视化测试")
    
    try:
        # 创建可视化器
        visualizer = TradingVisualizer()
        
        # 检查是否有数据
        df = visualizer.get_trade_data()
        if df.empty:
            print("📭 暂无交易数据，请先添加一些交易记录")
        else:
            print(f"📊 找到 {len(df)} 条交易记录")
            
            # 生成图表
            print("生成盈亏分析图...")
            visualizer.create_pnl_chart()
            
            print("生成统计分析图...")
            visualizer.create_statistics_chart()
            
            print("生成日历热力图...")
            visualizer.create_calendar_heatmap()
        
        visualizer.close()
        
    except ImportError as e:
        print(f"❌ 缺少依赖包: {e}")
        print("请安装: pip install matplotlib pandas")
    except Exception as e:
        print(f"❌ 可视化测试失败: {e}")
        import traceback
        traceback.print_exc()
