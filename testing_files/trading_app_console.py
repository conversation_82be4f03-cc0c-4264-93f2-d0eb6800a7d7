"""
交易复盘工具 - 控制台版本
专为解决GUI显示问题而设计的命令行版本
"""

import sqlite3
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List

class ConsoleTradingApp:
    """控制台版交易复盘工具"""
    
    def __init__(self):
        print("🚀 启动交易复盘工具 - 控制台版本")
        print("=" * 50)
        
        self.db_path = "trading_data.db"
        self.init_database()
        
    def init_database(self):
        """初始化数据库"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row
            
            # 创建表
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    position_size REAL NOT NULL,
                    direction TEXT NOT NULL,
                    result_pct REAL NOT NULL,
                    entry_time DATETIME NOT NULL,
                    exit_time DATETIME NOT NULL,
                    entry_fee_pct REAL NOT NULL DEFAULT 0.05,
                    exit_fee_pct REAL NOT NULL DEFAULT 0.05,
                    instrument TEXT DEFAULT '',
                    notes TEXT DEFAULT '',
                    gross_pnl REAL,
                    entry_fees REAL,
                    exit_fees REAL,
                    total_fees REAL,
                    net_pnl REAL,
                    duration_minutes REAL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            self.conn.commit()
            print("✅ 数据库初始化成功")
            
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            raise
    
    def calculate_metrics(self, position_size: float, result_pct: float, 
                         entry_fee_pct: float, exit_fee_pct: float,
                         entry_time: str, exit_time: str) -> Dict[str, float]:
        """计算交易指标"""
        gross_pnl = position_size * result_pct / 100
        entry_fees = position_size * entry_fee_pct / 100
        exit_fees = position_size * exit_fee_pct / 100
        total_fees = entry_fees + exit_fees
        net_pnl = gross_pnl - total_fees
        
        # 计算持仓时长
        try:
            entry_dt = datetime.fromisoformat(entry_time.replace('T', ' '))
            exit_dt = datetime.fromisoformat(exit_time.replace('T', ' '))
            duration_minutes = (exit_dt - entry_dt).total_seconds() / 60
        except:
            duration_minutes = 0
        
        return {
            'gross_pnl': round(gross_pnl, 2),
            'entry_fees': round(entry_fees, 2),
            'exit_fees': round(exit_fees, 2),
            'total_fees': round(total_fees, 2),
            'net_pnl': round(net_pnl, 2),
            'duration_minutes': round(duration_minutes, 2)
        }
    
    def get_input(self, prompt: str, default: str = "", input_type: type = str):
        """获取用户输入"""
        while True:
            try:
                if default:
                    user_input = input(f"{prompt} (默认: {default}): ").strip()
                    if not user_input:
                        user_input = default
                else:
                    user_input = input(f"{prompt}: ").strip()
                
                if input_type == float:
                    return float(user_input)
                elif input_type == int:
                    return int(user_input)
                else:
                    return user_input
                    
            except ValueError:
                print(f"❌ 输入错误，请输入有效的{input_type.__name__}类型")
            except KeyboardInterrupt:
                print("\n👋 用户取消操作")
                return None
    
    def add_trade(self):
        """添加交易记录"""
        print("\n📝 添加新的交易记录")
        print("-" * 30)
        
        try:
            # 获取交易信息
            position_size = self.get_input("💰 仓位大小(元)", "10000", float)
            if position_size is None:
                return
            
            direction = self.get_input("📈 交易方向(Long/Short)", "Long")
            if direction is None:
                return
            
            result_pct = self.get_input("🎯 交易结果(%)", "1.0", float)
            if result_pct is None:
                return
            
            # 时间输入
            now = datetime.now()
            default_entry = (now - timedelta(hours=1)).strftime("%Y-%m-%d %H:%M")
            default_exit = now.strftime("%Y-%m-%d %H:%M")
            
            entry_time = self.get_input("⏰ 开仓时间(YYYY-MM-DD HH:MM)", default_entry)
            if entry_time is None:
                return
            
            exit_time = self.get_input("⏰ 平仓时间(YYYY-MM-DD HH:MM)", default_exit)
            if exit_time is None:
                return
            
            entry_fee_pct = self.get_input("💸 开仓手续费率(%)", "0.05", float)
            if entry_fee_pct is None:
                return
            
            exit_fee_pct = self.get_input("💸 平仓手续费率(%)", "0.05", float)
            if exit_fee_pct is None:
                return
            
            instrument = self.get_input("📊 交易品种", "BTC/USDT")
            if instrument is None:
                return
            
            notes = self.get_input("📝 备注", "")
            if notes is None:
                return
            
            # 计算指标
            metrics = self.calculate_metrics(
                position_size, result_pct, entry_fee_pct, exit_fee_pct,
                entry_time, exit_time
            )
            
            # 显示计算结果
            print("\n📊 计算结果预览:")
            print(f"💵 毛盈亏: {metrics['gross_pnl']:+.2f} 元")
            print(f"💸 总手续费: -{metrics['total_fees']:.2f} 元")
            print(f"💰 净盈亏: {metrics['net_pnl']:+.2f} 元")
            print(f"📈 净收益率: {(metrics['net_pnl']/position_size*100):+.2f}%")
            print(f"⏱️ 持仓时长: {metrics['duration_minutes']:.0f} 分钟")
            
            # 确认保存
            confirm = self.get_input("\n💾 确认保存这条记录吗? (y/n)", "y")
            if confirm is None or confirm.lower() != 'y':
                print("❌ 取消保存")
                return
            
            # 保存到数据库
            trade_data = {
                'position_size': position_size,
                'direction': direction,
                'result_pct': result_pct,
                'entry_time': entry_time,
                'exit_time': exit_time,
                'entry_fee_pct': entry_fee_pct,
                'exit_fee_pct': exit_fee_pct,
                'instrument': instrument,
                'notes': notes,
                **metrics
            }
            
            self.conn.execute("""
                INSERT INTO trades (
                    position_size, direction, result_pct, entry_time, exit_time,
                    entry_fee_pct, exit_fee_pct, instrument, notes,
                    gross_pnl, entry_fees, exit_fees, total_fees, net_pnl, duration_minutes
                ) VALUES (
                    :position_size, :direction, :result_pct, :entry_time, :exit_time,
                    :entry_fee_pct, :exit_fee_pct, :instrument, :notes,
                    :gross_pnl, :entry_fees, :exit_fees, :total_fees, :net_pnl, :duration_minutes
                )
            """, trade_data)
            
            self.conn.commit()
            print("✅ 交易记录保存成功！")
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
    
    def view_trades(self):
        """查看交易记录"""
        print("\n📋 交易记录列表")
        print("-" * 80)
        
        try:
            cursor = self.conn.execute("""
                SELECT * FROM trades 
                ORDER BY entry_time DESC 
                LIMIT 20
            """)
            
            trades = cursor.fetchall()
            
            if not trades:
                print("📭 暂无交易记录")
                return
            
            # 表头
            print(f"{'ID':<4} {'时间':<16} {'方向':<6} {'仓位':<10} {'结果%':<8} {'净盈亏':<10} {'品种':<10}")
            print("-" * 80)
            
            # 数据行
            for trade in trades:
                print(f"{trade['id']:<4} {trade['entry_time'][:16]:<16} "
                      f"{trade['direction']:<6} {trade['position_size']:<10.0f} "
                      f"{trade['result_pct']:+<8.2f} {trade['net_pnl']:+<10.2f} "
                      f"{trade['instrument']:<10}")
            
            print("-" * 80)
            print(f"📊 显示最近 {len(trades)} 条记录")
            
        except Exception as e:
            print(f"❌ 查看记录失败: {e}")
    
    def show_statistics(self):
        """显示统计信息"""
        print("\n📈 交易统计分析")
        print("-" * 40)
        
        try:
            # 基础统计
            cursor = self.conn.execute("""
                SELECT 
                    COUNT(*) as total_trades,
                    SUM(CASE WHEN net_pnl > 0 THEN 1 ELSE 0 END) as winning_trades,
                    SUM(net_pnl) as total_pnl,
                    AVG(net_pnl) as avg_pnl,
                    MAX(net_pnl) as max_profit,
                    MIN(net_pnl) as max_loss,
                    SUM(total_fees) as total_fees
                FROM trades
            """)
            
            stats = cursor.fetchone()
            
            if stats['total_trades'] == 0:
                print("📭 暂无交易数据")
                return
            
            win_rate = (stats['winning_trades'] / stats['total_trades']) * 100
            
            print(f"📊 总交易次数: {stats['total_trades']} 笔")
            print(f"🎯 胜率: {win_rate:.1f}% ({stats['winning_trades']}/{stats['total_trades']})")
            print(f"💰 总盈亏: {stats['total_pnl']:+.2f} 元")
            print(f"📈 平均盈亏: {stats['avg_pnl']:+.2f} 元")
            print(f"🚀 最大盈利: {stats['max_profit']:+.2f} 元")
            print(f"📉 最大亏损: {stats['max_loss']:+.2f} 元")
            print(f"💸 总手续费: {stats['total_fees']:.2f} 元")
            
            # 方向统计
            cursor = self.conn.execute("""
                SELECT 
                    direction,
                    COUNT(*) as count,
                    SUM(CASE WHEN net_pnl > 0 THEN 1 ELSE 0 END) as wins,
                    AVG(net_pnl) as avg_pnl
                FROM trades 
                GROUP BY direction
            """)
            
            print("\n📊 分方向统计:")
            for row in cursor.fetchall():
                dir_win_rate = (row['wins'] / row['count']) * 100 if row['count'] > 0 else 0
                print(f"  {row['direction']}: {row['count']}笔, 胜率{dir_win_rate:.1f}%, 平均{row['avg_pnl']:+.2f}元")
            
        except Exception as e:
            print(f"❌ 统计分析失败: {e}")
    
    def show_menu(self):
        """显示主菜单"""
        print("\n" + "=" * 50)
        print("📊 交易复盘工具 - 主菜单")
        print("=" * 50)
        print("1. 📝 添加交易记录")
        print("2. 📋 查看交易记录")
        print("3. 📈 统计分析")
        print("4. 🧮 快速计算器")
        print("5. 🔚 退出程序")
        print("-" * 50)
    
    def quick_calculator(self):
        """快速计算器"""
        print("\n🧮 快速盈亏计算器")
        print("-" * 30)
        
        try:
            position_size = self.get_input("💰 仓位大小(元)", "10000", float)
            if position_size is None:
                return
            
            result_pct = self.get_input("🎯 交易结果(%)", "1.0", float)
            if result_pct is None:
                return
            
            fee_pct = self.get_input("💸 手续费率(%)", "0.05", float)
            if fee_pct is None:
                return
            
            # 计算
            gross_pnl = position_size * result_pct / 100
            total_fees = position_size * fee_pct / 100 * 2  # 开仓+平仓
            net_pnl = gross_pnl - total_fees
            net_rate = (net_pnl / position_size) * 100
            
            print("\n📊 计算结果:")
            print(f"💵 毛盈亏: {gross_pnl:+.2f} 元")
            print(f"💸 总手续费: -{total_fees:.2f} 元")
            print(f"💰 净盈亏: {net_pnl:+.2f} 元")
            print(f"📈 净收益率: {net_rate:+.2f}%")
            
        except Exception as e:
            print(f"❌ 计算失败: {e}")
    
    def run(self):
        """运行主程序"""
        print("🎉 程序启动成功！")
        
        while True:
            try:
                self.show_menu()
                choice = self.get_input("请选择操作", "1")
                
                if choice is None or choice == "5":
                    break
                elif choice == "1":
                    self.add_trade()
                elif choice == "2":
                    self.view_trades()
                elif choice == "3":
                    self.show_statistics()
                elif choice == "4":
                    self.quick_calculator()
                else:
                    print("❌ 无效选择，请重新输入")
                
            except KeyboardInterrupt:
                print("\n👋 用户中断程序")
                break
            except Exception as e:
                print(f"❌ 程序错误: {e}")
        
        # 清理资源
        try:
            self.conn.close()
            print("📝 数据库连接已关闭")
        except:
            pass
        
        print("🔚 程序已退出，感谢使用！")

def main():
    """主函数"""
    try:
        app = ConsoleTradingApp()
        app.run()
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")

if __name__ == "__main__":
    main()
