"""
GUI显示问题深度诊断工具
系统性排查macOS上tkinter显示问题的根本原因
"""

import tkinter as tk
import sys
import os
import platform
import subprocess
import time
import threading
from tkinter import messagebox

class GUIDeepDiagnosis:
    """GUI深度诊断类"""
    
    def __init__(self):
        self.results = []
        self.test_window = None
        
    def log(self, message, level="INFO"):
        """记录诊断信息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        print(log_entry)
        self.results.append(log_entry)
    
    def check_system_environment(self):
        """检查系统环境"""
        self.log("=== 系统环境检查 ===", "HEADER")
        
        # Python信息
        self.log(f"Python版本: {sys.version}")
        self.log(f"Python路径: {sys.executable}")
        self.log(f"平台: {platform.platform()}")
        self.log(f"架构: {platform.machine()}")
        
        # 检查是否在虚拟环境中
        if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
            self.log("检测到虚拟环境", "WARNING")
        else:
            self.log("使用系统Python", "INFO")
        
        # 检查GUI相关环境变量
        gui_vars = ['DISPLAY', 'WAYLAND_DISPLAY', 'XDG_SESSION_TYPE', 'DESKTOP_SESSION']
        for var in gui_vars:
            value = os.environ.get(var, '未设置')
            self.log(f"环境变量 {var}: {value}")
        
        # 检查是否在SSH会话中
        ssh_vars = ['SSH_CLIENT', 'SSH_TTY', 'SSH_CONNECTION']
        in_ssh = any(var in os.environ for var in ssh_vars)
        if in_ssh:
            self.log("检测到SSH会话 - 这可能导致GUI显示问题", "WARNING")
        else:
            self.log("非SSH会话", "INFO")
    
    def check_tkinter_details(self):
        """检查tkinter详细信息"""
        self.log("=== tkinter详细检查 ===", "HEADER")
        
        try:
            import tkinter
            self.log(f"tkinter版本: {tkinter.TkVersion}")
            self.log(f"tcl版本: {tkinter.TclVersion}")
            
            # 检查tkinter模块路径
            self.log(f"tkinter模块路径: {tkinter.__file__}")
            
            # 尝试创建根窗口但不显示
            self.log("尝试创建隐藏的根窗口...")
            test_root = tk.Tk()
            test_root.withdraw()  # 隐藏窗口
            
            # 获取窗口管理器信息
            try:
                wm_name = test_root.tk.call('wm', 'server', '.')
                self.log(f"窗口管理器: {wm_name}")
            except:
                self.log("无法获取窗口管理器信息", "WARNING")
            
            # 获取屏幕信息
            try:
                screen_width = test_root.winfo_screenwidth()
                screen_height = test_root.winfo_screenheight()
                self.log(f"屏幕尺寸: {screen_width}x{screen_height}")
            except:
                self.log("无法获取屏幕信息", "WARNING")
            
            test_root.destroy()
            self.log("隐藏根窗口测试成功", "SUCCESS")
            
        except Exception as e:
            self.log(f"tkinter检查失败: {e}", "ERROR")
            return False
        
        return True
    
    def check_display_system(self):
        """检查显示系统"""
        self.log("=== 显示系统检查 ===", "HEADER")
        
        # 检查macOS特定的显示信息
        try:
            # 检查显示器
            result = subprocess.run(['system_profiler', 'SPDisplaysDataType'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                displays = result.stdout.count('Resolution:')
                self.log(f"检测到 {displays} 个显示器")
            else:
                self.log("无法获取显示器信息", "WARNING")
        except Exception as e:
            self.log(f"显示器检查失败: {e}", "WARNING")
        
        # 检查窗口服务器
        try:
            result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
            if 'WindowServer' in result.stdout:
                self.log("WindowServer正在运行", "SUCCESS")
            else:
                self.log("WindowServer未运行", "ERROR")
        except:
            self.log("无法检查WindowServer状态", "WARNING")
    
    def test_basic_window_creation(self):
        """测试基础窗口创建"""
        self.log("=== 基础窗口创建测试 ===", "HEADER")
        
        try:
            self.log("步骤1: 创建根窗口...")
            root = tk.Tk()
            
            self.log("步骤2: 设置基本属性...")
            root.title("GUI诊断测试")
            root.geometry("300x200+100+100")
            
            self.log("步骤3: 尝试更新窗口...")
            root.update_idletasks()
            
            self.log("步骤4: 检查窗口状态...")
            try:
                state = root.state()
                self.log(f"窗口状态: {state}")
            except:
                self.log("无法获取窗口状态", "WARNING")
            
            self.log("步骤5: 销毁窗口...")
            root.destroy()
            self.log("基础窗口创建测试完成", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"基础窗口创建失败: {e}", "ERROR")
            import traceback
            self.log(f"详细错误: {traceback.format_exc()}", "ERROR")
            return False
    
    def test_window_visibility(self):
        """测试窗口可见性"""
        self.log("=== 窗口可见性测试 ===", "HEADER")
        
        try:
            self.log("创建测试窗口...")
            self.test_window = tk.Tk()
            self.test_window.title("可见性测试窗口")
            self.test_window.geometry("400x300")
            
            # 添加内容
            label = tk.Label(self.test_window, 
                           text="如果您看到这个窗口，\n请点击下面的按钮", 
                           font=("Arial", 14))
            label.pack(pady=50)
            
            self.window_visible = False
            
            def on_visible():
                self.window_visible = True
                self.log("用户确认窗口可见！", "SUCCESS")
                self.test_window.destroy()
            
            visible_btn = tk.Button(self.test_window, 
                                  text="我看到了窗口！", 
                                  command=on_visible,
                                  bg="green", fg="white",
                                  font=("Arial", 12))
            visible_btn.pack(pady=20)
            
            # 尝试各种显示方法
            self.log("尝试方法1: 基本显示...")
            self.test_window.update()
            
            self.log("尝试方法2: 强制置顶...")
            try:
                self.test_window.attributes('-topmost', True)
                self.test_window.lift()
                self.test_window.focus_force()
            except Exception as e:
                self.log(f"置顶失败: {e}", "WARNING")
            
            self.log("尝试方法3: 取消图标化...")
            try:
                self.test_window.deiconify()
            except Exception as e:
                self.log(f"取消图标化失败: {e}", "WARNING")
            
            self.log("尝试方法4: 强制刷新...")
            self.test_window.update_idletasks()
            
            # 设置超时
            def timeout():
                if not self.window_visible:
                    self.log("窗口可见性测试超时 - 窗口可能不可见", "ERROR")
                    try:
                        self.test_window.destroy()
                    except:
                        pass
            
            self.test_window.after(5000, timeout)
            
            self.log("窗口已创建，等待用户确认...")
            self.log("如果您看不到窗口，这表明存在显示问题", "WARNING")
            
            # 启动主循环
            self.test_window.mainloop()
            
            return self.window_visible
            
        except Exception as e:
            self.log(f"窗口可见性测试失败: {e}", "ERROR")
            return False
    
    def test_alternative_methods(self):
        """测试替代显示方法"""
        self.log("=== 替代显示方法测试 ===", "HEADER")
        
        methods = [
            ("方法1: 使用after延迟显示", self._test_delayed_show),
            ("方法2: 使用线程显示", self._test_threaded_show),
            ("方法3: 强制几何更新", self._test_geometry_update),
            ("方法4: 使用wm协议", self._test_wm_protocol)
        ]
        
        for method_name, method_func in methods:
            self.log(f"测试 {method_name}...")
            try:
                if method_func():
                    self.log(f"{method_name} 成功", "SUCCESS")
                    return True
                else:
                    self.log(f"{method_name} 失败", "WARNING")
            except Exception as e:
                self.log(f"{method_name} 异常: {e}", "ERROR")
        
        return False
    
    def _test_delayed_show(self):
        """测试延迟显示"""
        root = tk.Tk()
        root.withdraw()  # 先隐藏
        root.title("延迟显示测试")
        root.geometry("300x200")
        
        def show_window():
            root.deiconify()
            root.lift()
            root.attributes('-topmost', True)
            root.after(1000, lambda: root.attributes('-topmost', False))
        
        root.after(100, show_window)
        root.after(2000, root.destroy)
        
        try:
            root.mainloop()
            return True
        except:
            return False
    
    def _test_threaded_show(self):
        """测试线程显示"""
        success = False
        
        def create_window():
            nonlocal success
            try:
                root = tk.Tk()
                root.title("线程显示测试")
                root.geometry("300x200")
                root.after(1000, root.destroy)
                root.mainloop()
                success = True
            except:
                success = False
        
        thread = threading.Thread(target=create_window)
        thread.start()
        thread.join(timeout=3)
        
        return success
    
    def _test_geometry_update(self):
        """测试几何更新"""
        root = tk.Tk()
        root.title("几何更新测试")
        
        # 强制几何更新
        for i in range(5):
            root.geometry(f"30{i}x20{i}")
            root.update_idletasks()
            time.sleep(0.1)
        
        root.after(1000, root.destroy)
        
        try:
            root.mainloop()
            return True
        except:
            return False
    
    def _test_wm_protocol(self):
        """测试窗口管理器协议"""
        root = tk.Tk()
        root.title("WM协议测试")
        root.geometry("300x200")
        
        try:
            # 设置窗口管理器协议
            root.protocol("WM_DELETE_WINDOW", root.destroy)
            root.wm_attributes("-alpha", 0.9)  # 设置透明度
            root.after(1000, root.destroy)
            root.mainloop()
            return True
        except:
            return False
    
    def generate_report(self):
        """生成诊断报告"""
        self.log("=== 生成诊断报告 ===", "HEADER")
        
        report_file = "gui_diagnosis_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("GUI显示问题深度诊断报告\n")
            f.write("=" * 50 + "\n\n")
            
            for result in self.results:
                f.write(result + "\n")
        
        self.log(f"诊断报告已保存到: {report_file}", "SUCCESS")
    
    def run_full_diagnosis(self):
        """运行完整诊断"""
        self.log("开始GUI显示问题深度诊断...", "HEADER")
        
        # 系统环境检查
        self.check_system_environment()
        
        # tkinter详细检查
        if not self.check_tkinter_details():
            self.log("tkinter检查失败，无法继续", "ERROR")
            return False
        
        # 显示系统检查
        self.check_display_system()
        
        # 基础窗口创建测试
        if not self.test_basic_window_creation():
            self.log("基础窗口创建失败", "ERROR")
            return False
        
        # 窗口可见性测试
        self.log("即将进行窗口可见性测试，请注意观察屏幕...")
        input("按回车键继续窗口可见性测试...")
        
        if self.test_window_visibility():
            self.log("窗口可见性测试成功！", "SUCCESS")
            return True
        else:
            self.log("窗口可见性测试失败，尝试替代方法...", "WARNING")
            return self.test_alternative_methods()

def main():
    """主函数"""
    print("🔍 GUI显示问题深度诊断工具")
    print("=" * 50)
    
    diagnosis = GUIDeepDiagnosis()
    
    try:
        success = diagnosis.run_full_diagnosis()
        
        if success:
            print("\n✅ 诊断完成：GUI显示功能正常")
        else:
            print("\n❌ 诊断完成：发现GUI显示问题")
        
        diagnosis.generate_report()
        
    except KeyboardInterrupt:
        print("\n用户中断诊断")
    except Exception as e:
        print(f"\n诊断过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
