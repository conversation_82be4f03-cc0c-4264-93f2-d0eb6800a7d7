#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易复盘工具 - 完整版
实现HTML原型中的所有功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import os
import json
from datetime import datetime, timedelta
import threading
import time

class CompleteTradingApp:
    def __init__(self):
        """初始化应用程序"""
        print("🚀 启动完整版交易复盘工具...")

        # 定义现代化配色方案
        self.colors = {
            'bg_primary': '#0f0f23',
            'bg_secondary': '#1a1a2e',
            'bg_card': '#2c2c54',
            'bg_glass': '#3c3c6e',
            'text_primary': '#ffffff',
            'text_secondary': '#a0a0a0',
            'text_muted': '#666666',
            'accent_blue': '#667eea',
            'accent_purple': '#764ba2',
            'success_color': '#00ff88',
            'danger_color': '#ff4757',
            'warning_color': '#ffa502',
            'border_color': '#4a4a6a'
        }

        # 初始化数据库
        self.init_database()

        # 创建主窗口
        self.create_main_window()

        # 初始化变量（需要在主窗口创建后）
        self.init_variables()

        # 创建完整界面
        self.create_complete_interface()

        print("✅ 完整版交易复盘工具启动成功！")

    def init_variables(self):
        """初始化变量"""
        # 交易数据变量
        self.position_var = tk.StringVar(value="10000")
        self.direction_var = tk.StringVar(value="long")
        self.result_var = tk.StringVar(value="1.0")
        self.entry_fee_var = tk.StringVar(value="0.05")
        self.exit_fee_var = tk.StringVar(value="0.05")
        self.notes_var = tk.StringVar()

        # 计算结果变量
        self.gross_profit_var = tk.StringVar(value="+100元")
        self.entry_fees_var = tk.StringVar(value="-5元")
        self.exit_fees_var = tk.StringVar(value="-5元")
        self.total_fees_var = tk.StringVar(value="-10元")
        self.net_profit_var = tk.StringVar(value="+90元")
        self.return_rate_var = tk.StringVar(value="+0.90%")

        # 当前选中的快速按钮
        self.selected_position_btn = None
        self.selected_result_btn = None
        self.selected_entry_fee_btn = None
        self.selected_exit_fee_btn = None

    def init_database(self):
        """初始化数据库"""
        try:
            self.db_path = "trading_complete.db"
            self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
            self.cursor = self.conn.cursor()

            # 创建交易记录表
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    position_size REAL NOT NULL,
                    direction TEXT NOT NULL,
                    result_percent REAL NOT NULL,
                    entry_fee_percent REAL NOT NULL,
                    exit_fee_percent REAL NOT NULL,
                    gross_profit REAL NOT NULL,
                    total_fees REAL NOT NULL,
                    net_profit REAL NOT NULL,
                    return_rate REAL NOT NULL,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            self.conn.commit()
            print("✅ 数据库初始化成功")

        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            raise

    def create_main_window(self):
        """创建主窗口"""
        try:
            # 设置环境变量
            os.environ['TK_SILENCE_DEPRECATION'] = '1'

            self.root = tk.Tk()
            self.root.title("📊 交易复盘工具 - 完整版")

            # 设置窗口大小和位置
            window_width = 1400
            window_height = 900

            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2

            self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
            self.root.minsize(1200, 800)

            # 设置现代化背景
            self.root.configure(bg=self.colors['bg_primary'])

            print("✅ 主窗口创建成功")

        except Exception as e:
            print(f"❌ 主窗口创建失败: {e}")
            raise

    def create_complete_interface(self):
        """创建完整界面"""
        try:
            print("创建完整用户界面...")

            # 创建主容器
            self.create_main_container()

            # 创建标题区域
            self.create_header()

            # 创建标签页
            self.create_notebook()

            # 创建状态栏
            self.create_status_bar()

            # 启动实时计算
            self.start_real_time_calculation()

            print("✅ 完整用户界面创建成功")

        except Exception as e:
            print(f"❌ 完整用户界面创建失败: {e}")
            raise

    def create_main_container(self):
        """创建主容器"""
        # 主容器（模拟玻璃态效果）
        self.main_container = tk.Frame(
            self.root,
            bg=self.colors['bg_card'],
            relief='flat',
            bd=0
        )
        self.main_container.pack(fill='both', expand=True, padx=20, pady=20)

    def create_header(self):
        """创建标题区域"""
        # 标题容器
        header_frame = tk.Frame(
            self.main_container,
            bg=self.colors['accent_blue'],
            height=100
        )
        header_frame.pack(fill='x', pady=(0, 20))
        header_frame.pack_propagate(False)

        # 主标题
        title_label = tk.Label(
            header_frame,
            text="📊 交易复盘工具",
            font=('SF Pro Display', 24, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['accent_blue']
        )
        title_label.pack(expand=True)

        # 副标题
        subtitle_label = tk.Label(
            header_frame,
            text="快速录入，智能分析，轻松复盘",
            font=('SF Pro Display', 14),
            fg=self.colors['text_primary'],
            bg=self.colors['accent_blue']
        )
        subtitle_label.pack()

    def create_notebook(self):
        """创建标签页"""
        # 创建Notebook
        style = ttk.Style()
        style.theme_use('clam')

        # 配置标签页样式
        style.configure('TNotebook', background=self.colors['bg_card'])
        style.configure('TNotebook.Tab',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_secondary'],
                       padding=[20, 10],
                       font=('SF Pro Display', 12, 'bold'))
        style.map('TNotebook.Tab',
                 background=[('selected', self.colors['accent_blue'])],
                 foreground=[('selected', self.colors['text_primary'])])

        self.notebook = ttk.Notebook(self.main_container)
        self.notebook.pack(fill='both', expand=True)

        # 创建各个标签页
        self.create_quick_add_tab()
        self.create_journal_tab()
        self.create_analysis_tab()

    def create_quick_add_tab(self):
        """创建快速录入标签页"""
        # 快速录入页面
        quick_frame = tk.Frame(self.notebook, bg=self.colors['bg_primary'])
        self.notebook.add(quick_frame, text='⚡ 快速录入')

        # 创建滚动区域
        canvas = tk.Canvas(quick_frame, bg=self.colors['bg_primary'])
        scrollbar = ttk.Scrollbar(quick_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.colors['bg_primary'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 主内容区域
        content_frame = tk.Frame(scrollable_frame, bg=self.colors['bg_primary'])
        content_frame.pack(fill='both', expand=True, padx=40, pady=20)

        # 左右分栏
        left_frame = tk.Frame(content_frame, bg=self.colors['bg_primary'])
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 20))

        right_frame = tk.Frame(content_frame, bg=self.colors['bg_primary'])
        right_frame.pack(side='right', fill='both', expand=True, padx=(20, 0))

        # 创建左侧输入区域
        self.create_input_sections(left_frame)

        # 创建右侧预览区域
        self.create_preview_sections(right_frame)

        # 配置滚动
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 绑定鼠标滚轮
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

    def create_input_sections(self, parent):
        """创建输入区域"""
        # 仓位大小区域
        self.create_position_section(parent)

        # 交易方向区域
        self.create_direction_section(parent)

        # 交易结果区域
        self.create_result_section(parent)

        # 手续费区域
        self.create_fees_section(parent)

        # 备注区域
        self.create_notes_section(parent)

        # 操作按钮区域
        self.create_action_buttons(parent)

    def create_position_section(self, parent):
        """创建仓位大小区域"""
        # 区域容器
        section_frame = tk.LabelFrame(
            parent,
            text="💰 仓位大小",
            font=('SF Pro Display', 14, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_card'],
            bd=2,
            relief='groove'
        )
        section_frame.pack(fill='x', pady=(0, 15), padx=5)

        # 快速按钮区域
        btn_frame = tk.Frame(section_frame, bg=self.colors['bg_card'])
        btn_frame.pack(fill='x', pady=10, padx=10)

        # 快速按钮
        positions = [("5千", "5000"), ("1万", "10000"), ("2万", "20000"), ("5万", "50000")]
        self.position_buttons = []

        for text, value in positions:
            btn = tk.Button(
                btn_frame,
                text=text,
                command=lambda v=value: self.select_position(v),
                font=('SF Pro Display', 11, 'bold'),
                fg=self.colors['text_primary'],
                bg=self.colors['bg_secondary'],
                activebackground=self.colors['accent_blue'],
                activeforeground=self.colors['text_primary'],
                relief='flat',
                bd=0,
                padx=15,
                pady=8,
                cursor='hand2'
            )
            btn.pack(side='left', padx=3, fill='x', expand=True)
            self.position_buttons.append(btn)

            # 默认选中1万
            if value == "10000":
                btn.configure(bg=self.colors['accent_blue'])
                self.selected_position_btn = btn

        # 自定义输入
        custom_frame = tk.Frame(section_frame, bg=self.colors['bg_card'])
        custom_frame.pack(fill='x', pady=(0, 10), padx=10)

        tk.Label(
            custom_frame,
            text="自定义：",
            font=('SF Pro Display', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card']
        ).pack(side='left')

        self.position_entry = tk.Entry(
            custom_frame,
            textvariable=self.position_var,
            font=('SF Pro Display', 12),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            insertbackground=self.colors['text_primary'],
            relief='flat',
            bd=5,
            width=15
        )
        self.position_entry.pack(side='left', padx=(5, 0))
        self.position_entry.bind('<KeyRelease>', self.on_position_change)

        tk.Label(
            custom_frame,
            text="元",
            font=('SF Pro Display', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card']
        ).pack(side='left', padx=(5, 0))

    def create_direction_section(self, parent):
        """创建交易方向区域"""
        # 区域容器
        section_frame = tk.LabelFrame(
            parent,
            text="📈 交易方向",
            font=('SF Pro Display', 14, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_card'],
            bd=2,
            relief='groove'
        )
        section_frame.pack(fill='x', pady=(0, 15), padx=5)

        # 方向选择
        direction_frame = tk.Frame(section_frame, bg=self.colors['bg_card'])
        direction_frame.pack(fill='x', pady=15, padx=20)

        # 做多选项
        long_frame = tk.Frame(direction_frame, bg=self.colors['bg_card'])
        long_frame.pack(side='left', padx=(0, 30))

        self.long_radio = tk.Radiobutton(
            long_frame,
            text="📈 做多 (Long)",
            variable=self.direction_var,
            value="long",
            font=('SF Pro Display', 13, 'bold'),
            fg=self.colors['success_color'],
            bg=self.colors['bg_card'],
            selectcolor=self.colors['bg_secondary'],
            activebackground=self.colors['bg_card'],
            activeforeground=self.colors['success_color'],
            command=self.on_direction_change
        )
        self.long_radio.pack()

        # 做空选项
        short_frame = tk.Frame(direction_frame, bg=self.colors['bg_card'])
        short_frame.pack(side='left')

        self.short_radio = tk.Radiobutton(
            short_frame,
            text="📉 做空 (Short)",
            variable=self.direction_var,
            value="short",
            font=('SF Pro Display', 13, 'bold'),
            fg=self.colors['danger_color'],
            bg=self.colors['bg_card'],
            selectcolor=self.colors['bg_secondary'],
            activebackground=self.colors['bg_card'],
            activeforeground=self.colors['danger_color'],
            command=self.on_direction_change
        )
        self.short_radio.pack()

    def create_result_section(self, parent):
        """创建交易结果区域"""
        # 区域容器
        section_frame = tk.LabelFrame(
            parent,
            text="🎯 交易结果",
            font=('SF Pro Display', 14, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_card'],
            bd=2,
            relief='groove'
        )
        section_frame.pack(fill='x', pady=(0, 15), padx=5)

        # 快速按钮区域
        btn_frame = tk.Frame(section_frame, bg=self.colors['bg_card'])
        btn_frame.pack(fill='x', pady=10, padx=10)

        # 盈利按钮
        profit_frame = tk.Frame(btn_frame, bg=self.colors['bg_card'])
        profit_frame.pack(fill='x', pady=(0, 5))

        profit_results = [("+0.5%", "0.5"), ("+1.0%", "1.0"), ("+2.0%", "2.0")]
        self.profit_buttons = []

        for text, value in profit_results:
            btn = tk.Button(
                profit_frame,
                text=text,
                command=lambda v=value: self.select_result(v),
                font=('SF Pro Display', 11, 'bold'),
                fg=self.colors['text_primary'],
                bg=self.colors['success_color'],
                activebackground=self.colors['success_color'],
                activeforeground=self.colors['text_primary'],
                relief='flat',
                bd=0,
                padx=15,
                pady=8,
                cursor='hand2'
            )
            btn.pack(side='left', padx=3, fill='x', expand=True)
            self.profit_buttons.append(btn)

            # 默认选中+1.0%
            if value == "1.0":
                btn.configure(bg=self.colors['accent_blue'])
                self.selected_result_btn = btn

        # 亏损按钮
        loss_frame = tk.Frame(btn_frame, bg=self.colors['bg_card'])
        loss_frame.pack(fill='x')

        loss_results = [("-0.5%", "-0.5"), ("-1.0%", "-1.0"), ("-2.0%", "-2.0")]
        self.loss_buttons = []

        for text, value in loss_results:
            btn = tk.Button(
                loss_frame,
                text=text,
                command=lambda v=value: self.select_result(v),
                font=('SF Pro Display', 11, 'bold'),
                fg=self.colors['text_primary'],
                bg=self.colors['danger_color'],
                activebackground=self.colors['danger_color'],
                activeforeground=self.colors['text_primary'],
                relief='flat',
                bd=0,
                padx=15,
                pady=8,
                cursor='hand2'
            )
            btn.pack(side='left', padx=3, fill='x', expand=True)
            self.loss_buttons.append(btn)

        # 自定义输入
        custom_frame = tk.Frame(section_frame, bg=self.colors['bg_card'])
        custom_frame.pack(fill='x', pady=(0, 10), padx=10)

        tk.Label(
            custom_frame,
            text="自定义：",
            font=('SF Pro Display', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card']
        ).pack(side='left')

        self.result_entry = tk.Entry(
            custom_frame,
            textvariable=self.result_var,
            font=('SF Pro Display', 12),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            insertbackground=self.colors['text_primary'],
            relief='flat',
            bd=5,
            width=10
        )
        self.result_entry.pack(side='left', padx=(5, 0))
        self.result_entry.bind('<KeyRelease>', self.on_result_change)

        tk.Label(
            custom_frame,
            text="%",
            font=('SF Pro Display', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_card']
        ).pack(side='left', padx=(5, 0))

    def create_fees_section(self, parent):
        """创建手续费区域"""
        # 区域容器
        section_frame = tk.LabelFrame(
            parent,
            text="💸 手续费率",
            font=('SF Pro Display', 14, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_card'],
            bd=2,
            relief='groove'
        )
        section_frame.pack(fill='x', pady=(0, 15), padx=5)

        # 开仓手续费
        entry_fee_frame = tk.Frame(section_frame, bg=self.colors['bg_card'])
        entry_fee_frame.pack(fill='x', pady=10, padx=10)

        tk.Label(
            entry_fee_frame,
            text="开仓手续费：",
            font=('SF Pro Display', 12, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_card']
        ).pack(anchor='w', pady=(0, 5))

        # 开仓费率快速按钮
        entry_btn_frame = tk.Frame(entry_fee_frame, bg=self.colors['bg_card'])
        entry_btn_frame.pack(fill='x', pady=(0, 5))

        entry_fees = [("0.05%", "0.05"), ("0.1%", "0.1"), ("0.2%", "0.2")]
        self.entry_fee_buttons = []

        for text, value in entry_fees:
            btn = tk.Button(
                entry_btn_frame,
                text=text,
                command=lambda v=value: self.select_entry_fee(v),
                font=('SF Pro Display', 10, 'bold'),
                fg=self.colors['text_primary'],
                bg=self.colors['bg_secondary'],
                activebackground=self.colors['accent_blue'],
                activeforeground=self.colors['text_primary'],
                relief='flat',
                bd=0,
                padx=12,
                pady=6,
                cursor='hand2'
            )
            btn.pack(side='left', padx=2, fill='x', expand=True)
            self.entry_fee_buttons.append(btn)

            # 默认选中0.05%
            if value == "0.05":
                btn.configure(bg=self.colors['accent_blue'])
                self.selected_entry_fee_btn = btn

        # 平仓手续费
        exit_fee_frame = tk.Frame(section_frame, bg=self.colors['bg_card'])
        exit_fee_frame.pack(fill='x', pady=(0, 10), padx=10)

        tk.Label(
            exit_fee_frame,
            text="平仓手续费：",
            font=('SF Pro Display', 12, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_card']
        ).pack(anchor='w', pady=(0, 5))

        # 平仓费率快速按钮
        exit_btn_frame = tk.Frame(exit_fee_frame, bg=self.colors['bg_card'])
        exit_btn_frame.pack(fill='x')

        exit_fees = [("0.05%", "0.05"), ("0.1%", "0.1"), ("0.2%", "0.2")]
        self.exit_fee_buttons = []

        for text, value in exit_fees:
            btn = tk.Button(
                exit_btn_frame,
                text=text,
                command=lambda v=value: self.select_exit_fee(v),
                font=('SF Pro Display', 10, 'bold'),
                fg=self.colors['text_primary'],
                bg=self.colors['bg_secondary'],
                activebackground=self.colors['accent_blue'],
                activeforeground=self.colors['text_primary'],
                relief='flat',
                bd=0,
                padx=12,
                pady=6,
                cursor='hand2'
            )
            btn.pack(side='left', padx=2, fill='x', expand=True)
            self.exit_fee_buttons.append(btn)

            # 默认选中0.05%
            if value == "0.05":
                btn.configure(bg=self.colors['accent_blue'])
                self.selected_exit_fee_btn = btn

    def create_notes_section(self, parent):
        """创建备注区域"""
        # 区域容器
        section_frame = tk.LabelFrame(
            parent,
            text="📝 交易备注",
            font=('SF Pro Display', 14, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_card'],
            bd=2,
            relief='groove'
        )
        section_frame.pack(fill='x', pady=(0, 15), padx=5)

        # 备注输入框
        self.notes_text = tk.Text(
            section_frame,
            height=4,
            font=('SF Pro Display', 11),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            insertbackground=self.colors['text_primary'],
            relief='flat',
            bd=5,
            wrap='word'
        )
        self.notes_text.pack(fill='x', pady=10, padx=10)

        # 添加占位符文本
        placeholder = "记录交易原因、心得或其他重要信息..."
        self.notes_text.insert('1.0', placeholder)
        self.notes_text.configure(fg=self.colors['text_muted'])

        # 绑定焦点事件
        def on_focus_in(event):
            if self.notes_text.get('1.0', 'end-1c') == placeholder:
                self.notes_text.delete('1.0', 'end')
                self.notes_text.configure(fg=self.colors['text_primary'])

        def on_focus_out(event):
            if not self.notes_text.get('1.0', 'end-1c').strip():
                self.notes_text.insert('1.0', placeholder)
                self.notes_text.configure(fg=self.colors['text_muted'])

        self.notes_text.bind('<FocusIn>', on_focus_in)
        self.notes_text.bind('<FocusOut>', on_focus_out)

    def create_action_buttons(self, parent):
        """创建操作按钮区域"""
        # 按钮容器
        button_frame = tk.Frame(parent, bg=self.colors['bg_primary'])
        button_frame.pack(fill='x', pady=20, padx=5)

        # 主要操作按钮
        save_btn = tk.Button(
            button_frame,
            text="💾 保存交易",
            command=self.save_trade,
            font=('SF Pro Display', 14, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['accent_blue'],
            activebackground=self.colors['accent_purple'],
            activeforeground=self.colors['text_primary'],
            relief='flat',
            bd=0,
            padx=30,
            pady=12,
            cursor='hand2'
        )
        save_btn.pack(fill='x', pady=(0, 10))

        # 次要操作按钮
        secondary_frame = tk.Frame(button_frame, bg=self.colors['bg_primary'])
        secondary_frame.pack(fill='x')

        clear_btn = tk.Button(
            secondary_frame,
            text="🗑️ 清空表单",
            command=self.clear_form,
            font=('SF Pro Display', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_secondary'],
            activebackground=self.colors['danger_color'],
            activeforeground=self.colors['text_primary'],
            relief='flat',
            bd=0,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        clear_btn.pack(side='left', fill='x', expand=True, padx=(0, 5))

        copy_btn = tk.Button(
            secondary_frame,
            text="📋 复制上一笔",
            command=self.copy_last_trade,
            font=('SF Pro Display', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_secondary'],
            activebackground=self.colors['warning_color'],
            activeforeground=self.colors['text_primary'],
            relief='flat',
            bd=0,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        copy_btn.pack(side='right', fill='x', expand=True, padx=(5, 0))

    def create_preview_sections(self, parent):
        """创建预览区域"""
        # 实时计算预览
        self.create_calculation_preview(parent)

        # 统计信息预览
        self.create_stats_preview(parent)

    def create_calculation_preview(self, parent):
        """创建实时计算预览"""
        # 预览容器
        preview_frame = tk.LabelFrame(
            parent,
            text="💰 实时计算预览",
            font=('SF Pro Display', 16, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['accent_blue'],
            bd=2,
            relief='groove'
        )
        preview_frame.pack(fill='x', pady=(0, 20), padx=5)

        # 计算结果网格
        calc_grid = tk.Frame(preview_frame, bg=self.colors['accent_blue'])
        calc_grid.pack(fill='x', pady=15, padx=15)

        # 创建计算项目
        calc_items = [
            ("毛收益", self.gross_profit_var, 0, 0),
            ("开仓费用", self.entry_fees_var, 0, 1),
            ("平仓费用", self.exit_fees_var, 1, 0),
            ("总手续费", self.total_fees_var, 1, 1),
            ("净收益", self.net_profit_var, 2, 0),
            ("净收益率", self.return_rate_var, 2, 1)
        ]

        for label_text, var, row, col in calc_items:
            # 计算项容器
            item_frame = tk.Frame(calc_grid, bg=self.colors['bg_card'], relief='raised', bd=1)
            item_frame.grid(row=row, column=col, padx=5, pady=5, sticky='ew')

            # 标签
            label = tk.Label(
                item_frame,
                text=label_text,
                font=('SF Pro Display', 10),
                fg=self.colors['text_secondary'],
                bg=self.colors['bg_card']
            )
            label.pack(pady=(8, 2))

            # 数值
            value_label = tk.Label(
                item_frame,
                textvariable=var,
                font=('SF Pro Display', 14, 'bold'),
                fg=self.colors['text_primary'],
                bg=self.colors['bg_card']
            )
            value_label.pack(pady=(0, 8))

        # 配置网格权重
        calc_grid.grid_columnconfigure(0, weight=1)
        calc_grid.grid_columnconfigure(1, weight=1)

    def create_stats_preview(self, parent):
        """创建统计信息预览"""
        # 统计容器
        stats_frame = tk.LabelFrame(
            parent,
            text="📊 交易统计",
            font=('SF Pro Display', 16, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_card'],
            bd=2,
            relief='groove'
        )
        stats_frame.pack(fill='both', expand=True, padx=5)

        # 统计内容
        stats_content = tk.Frame(stats_frame, bg=self.colors['bg_card'])
        stats_content.pack(fill='both', expand=True, pady=15, padx=15)

        # 今日统计
        today_frame = tk.Frame(stats_content, bg=self.colors['bg_secondary'], relief='raised', bd=1)
        today_frame.pack(fill='x', pady=(0, 10))

        tk.Label(
            today_frame,
            text="📅 今日统计",
            font=('SF Pro Display', 14, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        ).pack(pady=10)

        # 今日数据
        today_data = tk.Frame(today_frame, bg=self.colors['bg_secondary'])
        today_data.pack(fill='x', padx=15, pady=(0, 10))

        self.today_trades_var = tk.StringVar(value="0笔")
        self.today_profit_var = tk.StringVar(value="+0元")
        self.today_winrate_var = tk.StringVar(value="0%")

        today_items = [
            ("交易次数", self.today_trades_var),
            ("总盈亏", self.today_profit_var),
            ("胜率", self.today_winrate_var)
        ]

        for label_text, var in today_items:
            item_frame = tk.Frame(today_data, bg=self.colors['bg_secondary'])
            item_frame.pack(fill='x', pady=2)

            tk.Label(
                item_frame,
                text=f"{label_text}:",
                font=('SF Pro Display', 11),
                fg=self.colors['text_secondary'],
                bg=self.colors['bg_secondary']
            ).pack(side='left')

            tk.Label(
                item_frame,
                textvariable=var,
                font=('SF Pro Display', 11, 'bold'),
                fg=self.colors['text_primary'],
                bg=self.colors['bg_secondary']
            ).pack(side='right')

        # 本月统计
        month_frame = tk.Frame(stats_content, bg=self.colors['bg_secondary'], relief='raised', bd=1)
        month_frame.pack(fill='x')

        tk.Label(
            month_frame,
            text="📈 本月统计",
            font=('SF Pro Display', 14, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        ).pack(pady=10)

        # 本月数据
        month_data = tk.Frame(month_frame, bg=self.colors['bg_secondary'])
        month_data.pack(fill='x', padx=15, pady=(0, 10))

        self.month_trades_var = tk.StringVar(value="0笔")
        self.month_profit_var = tk.StringVar(value="+0元")
        self.month_winrate_var = tk.StringVar(value="0%")

        month_items = [
            ("交易次数", self.month_trades_var),
            ("总盈亏", self.month_profit_var),
            ("胜率", self.month_winrate_var)
        ]

        for label_text, var in month_items:
            item_frame = tk.Frame(month_data, bg=self.colors['bg_secondary'])
            item_frame.pack(fill='x', pady=2)

            tk.Label(
                item_frame,
                text=f"{label_text}:",
                font=('SF Pro Display', 11),
                fg=self.colors['text_secondary'],
                bg=self.colors['bg_secondary']
            ).pack(side='left')

            tk.Label(
                item_frame,
                textvariable=var,
                font=('SF Pro Display', 11, 'bold'),
                fg=self.colors['text_primary'],
                bg=self.colors['bg_secondary']
            ).pack(side='right')

    # 事件处理函数
    def select_position(self, value):
        """选择仓位大小"""
        # 重置所有按钮颜色
        for btn in self.position_buttons:
            btn.configure(bg=self.colors['bg_secondary'])

        # 高亮选中按钮
        for btn in self.position_buttons:
            if btn.cget('text') == self.get_position_text(value):
                btn.configure(bg=self.colors['accent_blue'])
                self.selected_position_btn = btn
                break

        # 更新变量
        self.position_var.set(value)
        self.calculate_real_time()

    def get_position_text(self, value):
        """根据数值获取按钮文本"""
        mapping = {"5000": "5千", "10000": "1万", "20000": "2万", "50000": "5万"}
        return mapping.get(value, value)

    def select_result(self, value):
        """选择交易结果"""
        # 重置所有按钮颜色
        for btn in self.profit_buttons + self.loss_buttons:
            if btn in self.profit_buttons:
                btn.configure(bg=self.colors['success_color'])
            else:
                btn.configure(bg=self.colors['danger_color'])

        # 高亮选中按钮
        for btn in self.profit_buttons + self.loss_buttons:
            if self.get_result_text(value) in btn.cget('text'):
                btn.configure(bg=self.colors['accent_blue'])
                self.selected_result_btn = btn
                break

        # 更新变量
        self.result_var.set(value)
        self.calculate_real_time()

    def get_result_text(self, value):
        """根据数值获取结果文本"""
        if float(value) >= 0:
            return f"+{value}%"
        else:
            return f"{value}%"

    def select_entry_fee(self, value):
        """选择开仓手续费"""
        # 重置所有按钮颜色
        for btn in self.entry_fee_buttons:
            btn.configure(bg=self.colors['bg_secondary'])

        # 高亮选中按钮
        for btn in self.entry_fee_buttons:
            if f"{value}%" in btn.cget('text'):
                btn.configure(bg=self.colors['accent_blue'])
                self.selected_entry_fee_btn = btn
                break

        # 更新变量
        self.entry_fee_var.set(value)
        self.calculate_real_time()

    def select_exit_fee(self, value):
        """选择平仓手续费"""
        # 重置所有按钮颜色
        for btn in self.exit_fee_buttons:
            btn.configure(bg=self.colors['bg_secondary'])

        # 高亮选中按钮
        for btn in self.exit_fee_buttons:
            if f"{value}%" in btn.cget('text'):
                btn.configure(bg=self.colors['accent_blue'])
                self.selected_exit_fee_btn = btn
                break

        # 更新变量
        self.exit_fee_var.set(value)
        self.calculate_real_time()

    def on_position_change(self, event=None):
        """仓位输入变化事件"""
        self.calculate_real_time()

    def on_direction_change(self):
        """交易方向变化事件"""
        self.calculate_real_time()

    def on_result_change(self, event=None):
        """交易结果变化事件"""
        self.calculate_real_time()

    def calculate_real_time(self):
        """实时计算"""
        try:
            # 获取输入值
            position = float(self.position_var.get() or 0)
            result_percent = float(self.result_var.get() or 0)
            entry_fee_percent = float(self.entry_fee_var.get() or 0)
            exit_fee_percent = float(self.exit_fee_var.get() or 0)

            # 计算毛收益
            gross_profit = position * result_percent / 100

            # 计算手续费
            entry_fees = position * entry_fee_percent / 100
            exit_fees = position * exit_fee_percent / 100
            total_fees = entry_fees + exit_fees

            # 计算净收益
            net_profit = gross_profit - total_fees

            # 计算净收益率
            return_rate = (net_profit / position) * 100 if position > 0 else 0

            # 更新显示
            self.gross_profit_var.set(f"{gross_profit:+.0f}元")
            self.entry_fees_var.set(f"-{entry_fees:.0f}元")
            self.exit_fees_var.set(f"-{exit_fees:.0f}元")
            self.total_fees_var.set(f"-{total_fees:.0f}元")
            self.net_profit_var.set(f"{net_profit:+.0f}元")
            self.return_rate_var.set(f"{return_rate:+.2f}%")

        except (ValueError, ZeroDivisionError):
            # 输入无效时显示默认值
            self.gross_profit_var.set("+0元")
            self.entry_fees_var.set("-0元")
            self.exit_fees_var.set("-0元")
            self.total_fees_var.set("-0元")
            self.net_profit_var.set("+0元")
            self.return_rate_var.set("+0.00%")

    def start_real_time_calculation(self):
        """启动实时计算"""
        def update_loop():
            while True:
                try:
                    self.root.after(0, self.calculate_real_time)
                    self.root.after(0, self.update_statistics)
                    time.sleep(1)  # 每秒更新一次
                except:
                    break

        # 在后台线程中运行更新循环
        update_thread = threading.Thread(target=update_loop, daemon=True)
        update_thread.start()

    def update_statistics(self):
        """更新统计信息"""
        try:
            # 获取今日统计
            today = datetime.now().strftime('%Y-%m-%d')
            self.cursor.execute('''
                SELECT COUNT(*), SUM(net_profit),
                       SUM(CASE WHEN net_profit > 0 THEN 1 ELSE 0 END) as wins
                FROM trades
                WHERE date = ?
            ''', (today,))

            today_result = self.cursor.fetchone()
            if today_result and today_result[0] > 0:
                trades_count, total_profit, wins = today_result
                winrate = (wins / trades_count) * 100 if trades_count > 0 else 0

                self.today_trades_var.set(f"{trades_count}笔")
                self.today_profit_var.set(f"{total_profit:+.0f}元")
                self.today_winrate_var.set(f"{winrate:.1f}%")
            else:
                self.today_trades_var.set("0笔")
                self.today_profit_var.set("+0元")
                self.today_winrate_var.set("0%")

            # 获取本月统计
            month = datetime.now().strftime('%Y-%m')
            self.cursor.execute('''
                SELECT COUNT(*), SUM(net_profit),
                       SUM(CASE WHEN net_profit > 0 THEN 1 ELSE 0 END) as wins
                FROM trades
                WHERE date LIKE ?
            ''', (f"{month}%",))

            month_result = self.cursor.fetchone()
            if month_result and month_result[0] > 0:
                trades_count, total_profit, wins = month_result
                winrate = (wins / trades_count) * 100 if trades_count > 0 else 0

                self.month_trades_var.set(f"{trades_count}笔")
                self.month_profit_var.set(f"{total_profit:+.0f}元")
                self.month_winrate_var.set(f"{winrate:.1f}%")
            else:
                self.month_trades_var.set("0笔")
                self.month_profit_var.set("+0元")
                self.month_winrate_var.set("0%")

        except Exception as e:
            print(f"统计更新失败: {e}")

    def save_trade(self):
        """保存交易记录"""
        try:
            # 获取当前数据
            position = float(self.position_var.get() or 0)
            direction = self.direction_var.get()
            result_percent = float(self.result_var.get() or 0)
            entry_fee_percent = float(self.entry_fee_var.get() or 0)
            exit_fee_percent = float(self.exit_fee_var.get() or 0)

            # 获取备注
            notes = self.notes_text.get('1.0', 'end-1c').strip()
            if notes == "记录交易原因、心得或其他重要信息...":
                notes = ""

            # 计算各项数值
            gross_profit = position * result_percent / 100
            entry_fees = position * entry_fee_percent / 100
            exit_fees = position * exit_fee_percent / 100
            total_fees = entry_fees + exit_fees
            net_profit = gross_profit - total_fees
            return_rate = (net_profit / position) * 100 if position > 0 else 0

            # 保存到数据库
            today = datetime.now().strftime('%Y-%m-%d')
            self.cursor.execute('''
                INSERT INTO trades (
                    date, position_size, direction, result_percent,
                    entry_fee_percent, exit_fee_percent, gross_profit,
                    total_fees, net_profit, return_rate, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                today, position, direction, result_percent,
                entry_fee_percent, exit_fee_percent, gross_profit,
                total_fees, net_profit, return_rate, notes
            ))

            self.conn.commit()

            # 显示成功消息
            messagebox.showinfo("保存成功", f"交易记录已保存！\n净收益: {net_profit:+.2f}元")

            # 更新统计
            self.update_statistics()

        except Exception as e:
            messagebox.showerror("保存失败", f"保存交易记录时出错：{e}")

    def clear_form(self):
        """清空表单"""
        # 重置所有变量
        self.position_var.set("10000")
        self.direction_var.set("long")
        self.result_var.set("1.0")
        self.entry_fee_var.set("0.05")
        self.exit_fee_var.set("0.05")

        # 重置按钮状态
        self.reset_button_states()

        # 清空备注
        self.notes_text.delete('1.0', 'end')
        self.notes_text.insert('1.0', "记录交易原因、心得或其他重要信息...")
        self.notes_text.configure(fg=self.colors['text_muted'])

        # 重新计算
        self.calculate_real_time()

    def reset_button_states(self):
        """重置按钮状态"""
        # 重置仓位按钮
        for btn in self.position_buttons:
            btn.configure(bg=self.colors['bg_secondary'])
            if btn.cget('text') == "1万":
                btn.configure(bg=self.colors['accent_blue'])
                self.selected_position_btn = btn

        # 重置结果按钮
        for btn in self.profit_buttons + self.loss_buttons:
            if btn in self.profit_buttons:
                btn.configure(bg=self.colors['success_color'])
                if "+1.0%" in btn.cget('text'):
                    btn.configure(bg=self.colors['accent_blue'])
                    self.selected_result_btn = btn
            else:
                btn.configure(bg=self.colors['danger_color'])

        # 重置手续费按钮
        for btn in self.entry_fee_buttons + self.exit_fee_buttons:
            btn.configure(bg=self.colors['bg_secondary'])
            if "0.05%" in btn.cget('text'):
                btn.configure(bg=self.colors['accent_blue'])
                if btn in self.entry_fee_buttons:
                    self.selected_entry_fee_btn = btn
                else:
                    self.selected_exit_fee_btn = btn

    def copy_last_trade(self):
        """复制上一笔交易"""
        try:
            # 获取最后一笔交易
            self.cursor.execute('''
                SELECT position_size, direction, result_percent,
                       entry_fee_percent, exit_fee_percent, notes
                FROM trades
                ORDER BY created_at DESC
                LIMIT 1
            ''')

            result = self.cursor.fetchone()
            if result:
                position, direction, result_percent, entry_fee, exit_fee, notes = result

                # 设置表单值
                self.position_var.set(str(int(position)))
                self.direction_var.set(direction)
                self.result_var.set(str(result_percent))
                self.entry_fee_var.set(str(entry_fee))
                self.exit_fee_var.set(str(exit_fee))

                # 设置备注
                if notes:
                    self.notes_text.delete('1.0', 'end')
                    self.notes_text.insert('1.0', notes)
                    self.notes_text.configure(fg=self.colors['text_primary'])

                # 更新按钮状态
                self.update_button_states_from_values()

                # 重新计算
                self.calculate_real_time()

                messagebox.showinfo("复制成功", "已复制上一笔交易的数据")
            else:
                messagebox.showwarning("无数据", "没有找到历史交易记录")

        except Exception as e:
            messagebox.showerror("复制失败", f"复制交易数据时出错：{e}")

    def update_button_states_from_values(self):
        """根据当前值更新按钮状态"""
        # 更新仓位按钮
        position_value = self.position_var.get()
        for btn in self.position_buttons:
            btn.configure(bg=self.colors['bg_secondary'])
            if btn.cget('text') == self.get_position_text(position_value):
                btn.configure(bg=self.colors['accent_blue'])
                self.selected_position_btn = btn

        # 更新结果按钮
        result_value = self.result_var.get()
        for btn in self.profit_buttons + self.loss_buttons:
            if btn in self.profit_buttons:
                btn.configure(bg=self.colors['success_color'])
            else:
                btn.configure(bg=self.colors['danger_color'])

            if self.get_result_text(result_value) in btn.cget('text'):
                btn.configure(bg=self.colors['accent_blue'])
                self.selected_result_btn = btn

        # 更新手续费按钮
        entry_fee_value = self.entry_fee_var.get()
        exit_fee_value = self.exit_fee_var.get()

        for btn in self.entry_fee_buttons:
            btn.configure(bg=self.colors['bg_secondary'])
            if f"{entry_fee_value}%" in btn.cget('text'):
                btn.configure(bg=self.colors['accent_blue'])
                self.selected_entry_fee_btn = btn

        for btn in self.exit_fee_buttons:
            btn.configure(bg=self.colors['bg_secondary'])
            if f"{exit_fee_value}%" in btn.cget('text'):
                btn.configure(bg=self.colors['accent_blue'])
                self.selected_exit_fee_btn = btn

    def create_journal_tab(self):
        """创建交易日记标签页"""
        # 交易日记页面
        journal_frame = tk.Frame(self.notebook, bg=self.colors['bg_primary'])
        self.notebook.add(journal_frame, text='📅 交易日记')

        # 占位符内容
        placeholder_label = tk.Label(
            journal_frame,
            text="📅 交易日记功能\n\n这里将显示交易日历和历史记录\n敬请期待...",
            font=('SF Pro Display', 16),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_primary']
        )
        placeholder_label.pack(expand=True)

    def create_analysis_tab(self):
        """创建数据分析标签页"""
        # 数据分析页面
        analysis_frame = tk.Frame(self.notebook, bg=self.colors['bg_primary'])
        self.notebook.add(analysis_frame, text='📈 数据分析')

        # 占位符内容
        placeholder_label = tk.Label(
            analysis_frame,
            text="📈 数据分析功能\n\n这里将显示各种统计图表和分析报告\n敬请期待...",
            font=('SF Pro Display', 16),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_primary']
        )
        placeholder_label.pack(expand=True)

    def create_status_bar(self):
        """创建状态栏"""
        # 状态栏
        status_frame = tk.Frame(
            self.main_container,
            bg=self.colors['bg_secondary'],
            height=30
        )
        status_frame.pack(fill='x', pady=(10, 0))
        status_frame.pack_propagate(False)

        # 状态信息
        self.status_var = tk.StringVar(value="✅ 系统就绪 - 完整版交易复盘工具")
        status_label = tk.Label(
            status_frame,
            textvariable=self.status_var,
            font=('SF Pro Display', 10),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_secondary']
        )
        status_label.pack(side='left', padx=10, pady=5)

        # 时间显示
        self.time_var = tk.StringVar()
        time_label = tk.Label(
            status_frame,
            textvariable=self.time_var,
            font=('SF Pro Display', 10),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_secondary']
        )
        time_label.pack(side='right', padx=10, pady=5)

        # 更新时间
        self.update_time()

    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.time_var.set(current_time)
        self.root.after(1000, self.update_time)

    def run(self):
        """运行应用程序"""
        try:
            print("🚀 启动完整版GUI主循环...")
            print("💡 完整版交易复盘工具已启动！")

            # 强制窗口显示在前台
            self.root.lift()
            self.root.attributes('-topmost', True)
            self.root.after(1000, lambda: self.root.attributes('-topmost', False))

            # 启动主循环
            self.root.mainloop()
            print("🔚 完整版应用程序已退出")

        except Exception as e:
            print(f"❌ 完整版应用程序运行失败: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 关闭数据库连接
            if hasattr(self, 'conn'):
                self.conn.close()

def main():
    """主函数"""
    print("=" * 60)
    print("📊 交易复盘工具 - 完整版")
    print("=" * 60)
    print("🎯 实现HTML原型中的所有功能")
    print("✨ 现代化界面设计")
    print("⚡ 实时计算预览")
    print("📊 统计信息显示")
    print("💾 数据持久化存储")
    print("=" * 60)

    try:
        app = CompleteTradingApp()
        app.run()
    except Exception as e:
        print(f"❌ 完整版应用程序启动失败: {e}")
        print("💡 可能的解决方案:")
        print("   1. 检查Python是否支持tkinter")
        print("   2. 在macOS上可能需要: brew install python-tk")
        print("   3. 确保在支持GUI的环境中运行")
        print("   4. 检查数据库权限")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()