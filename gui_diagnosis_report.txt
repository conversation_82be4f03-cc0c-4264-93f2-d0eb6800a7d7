GUI显示问题深度诊断报告
==================================================

[21:55:54] HEADER: 开始GUI显示问题深度诊断...
[21:55:54] HEADER: === 系统环境检查 ===
[21:55:54] INFO: Python版本: 3.9.6 (default, Mar 12 2025, 20:22:46) 
[Clang 17.0.0 (clang-1700.0.13.3)]
[21:55:54] INFO: Python路径: /Library/Developer/CommandLineTools/usr/bin/python3
[21:55:54] INFO: 平台: macOS-15.5-arm64-arm-64bit
[21:55:54] INFO: 架构: arm64
[21:55:54] INFO: 使用系统Python
[21:55:54] INFO: 环境变量 DISPLAY: 未设置
[21:55:54] INFO: 环境变量 WAYLAND_DISPLAY: 未设置
[21:55:54] INFO: 环境变量 XDG_SESSION_TYPE: 未设置
[21:55:54] INFO: 环境变量 DESKTOP_SESSION: 未设置
[21:55:54] INFO: 非SSH会话
[21:55:54] HEADER: === tkinter详细检查 ===
[21:55:54] INFO: tkinter版本: 8.5
[21:55:54] INFO: tcl版本: 8.5
[21:55:54] INFO: tkinter模块路径: /Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tkinter/__init__.py
[21:55:54] INFO: 尝试创建隐藏的根窗口...
[21:55:54] WARNING: 无法获取窗口管理器信息
[21:55:54] INFO: 屏幕尺寸: 3440x1440
[21:55:54] SUCCESS: 隐藏根窗口测试成功
[21:55:54] HEADER: === 显示系统检查 ===
[21:55:54] INFO: 检测到 2 个显示器
[21:55:54] SUCCESS: WindowServer正在运行
[21:55:54] HEADER: === 基础窗口创建测试 ===
[21:55:54] INFO: 步骤1: 创建根窗口...
[21:55:54] INFO: 步骤2: 设置基本属性...
[21:55:54] INFO: 步骤3: 尝试更新窗口...
[21:55:54] INFO: 步骤4: 检查窗口状态...
[21:55:54] INFO: 窗口状态: normal
[21:55:54] INFO: 步骤5: 销毁窗口...
[21:55:54] SUCCESS: 基础窗口创建测试完成
[21:55:54] INFO: 即将进行窗口可见性测试，请注意观察屏幕...
[21:56:13] HEADER: === 窗口可见性测试 ===
[21:56:13] INFO: 创建测试窗口...
[21:56:13] INFO: 尝试方法1: 基本显示...
[21:56:25] SUCCESS: 用户确认窗口可见！
[21:56:25] INFO: 尝试方法2: 强制置顶...
[21:56:25] WARNING: 置顶失败: can't invoke "wm" command:  application has been destroyed
[21:56:25] INFO: 尝试方法3: 取消图标化...
[21:56:25] WARNING: 取消图标化失败: can't invoke "wm" command:  application has been destroyed
[21:56:25] INFO: 尝试方法4: 强制刷新...
[21:56:25] ERROR: 窗口可见性测试失败: can't invoke "update" command:  application has been destroyed
[21:56:25] WARNING: 窗口可见性测试失败，尝试替代方法...
[21:56:25] HEADER: === 替代显示方法测试 ===
[21:56:25] INFO: 测试 方法1: 使用after延迟显示...
[21:56:27] SUCCESS: 方法1: 使用after延迟显示 成功
[21:56:27] HEADER: === 生成诊断报告 ===
