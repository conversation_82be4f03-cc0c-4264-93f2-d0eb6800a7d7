"""
交易复盘工具 - 命令行版本
确保核心功能正常工作的备用方案
"""

import sqlite3
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List

class TradingCLI:
    """命令行版交易复盘工具"""
    
    def __init__(self, db_path: str = "trading_cli.db"):
        """初始化命令行工具"""
        self.db_path = db_path
        self.connection = None
        self.init_database()
        print("🚀 交易复盘工具 - 命令行版本启动成功")
        print("=" * 50)
    
    def init_database(self):
        """初始化数据库"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row
            
            # 创建交易记录表
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                position_size REAL NOT NULL,
                direction TEXT NOT NULL,
                result_pct REAL NOT NULL,
                entry_time DATETIME NOT NULL,
                exit_time DATETIME NOT NULL,
                entry_fee_pct REAL NOT NULL DEFAULT 0.05,
                exit_fee_pct REAL NOT NULL DEFAULT 0.05,
                instrument TEXT DEFAULT '',
                notes TEXT DEFAULT '',
                gross_pnl REAL,
                entry_fees REAL,
                exit_fees REAL,
                total_fees REAL,
                net_pnl REAL,
                duration_minutes REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            """
            
            self.connection.execute(create_table_sql)
            self.connection.commit()
            print("✅ 数据库初始化成功")
            
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            raise
    
    def calculate_metrics(self, position_size: float, result_pct: float, 
                         entry_fee_pct: float, exit_fee_pct: float,
                         entry_time: str, exit_time: str) -> Dict[str, float]:
        """计算交易指标"""
        # 基础计算
        gross_pnl = position_size * result_pct / 100
        entry_fees = position_size * entry_fee_pct / 100
        exit_fees = position_size * exit_fee_pct / 100
        total_fees = entry_fees + exit_fees
        net_pnl = gross_pnl - total_fees
        
        # 计算持仓时长
        try:
            entry_dt = datetime.fromisoformat(entry_time.replace('T', ' '))
            exit_dt = datetime.fromisoformat(exit_time.replace('T', ' '))
            duration_minutes = (exit_dt - entry_dt).total_seconds() / 60
        except:
            duration_minutes = 0
        
        return {
            'gross_pnl': round(gross_pnl, 2),
            'entry_fees': round(entry_fees, 2),
            'exit_fees': round(exit_fees, 2),
            'total_fees': round(total_fees, 2),
            'net_pnl': round(net_pnl, 2),
            'duration_minutes': round(duration_minutes, 2)
        }
    
    def add_trade_interactive(self):
        """交互式添加交易记录"""
        print("\n📝 添加新的交易记录")
        print("-" * 30)
        
        try:
            # 获取用户输入
            print("请输入交易信息:")
            
            # 仓位大小
            while True:
                try:
                    position_size = float(input("💰 仓位大小 (元): "))
                    if position_size > 0:
                        break
                    else:
                        print("❌ 仓位大小必须大于0")
                except ValueError:
                    print("❌ 请输入有效的数字")
            
            # 交易方向
            while True:
                direction = input("📈 交易方向 (Long/Short): ").strip()
                if direction.lower() in ['long', 'short']:
                    direction = direction.capitalize()
                    break
                else:
                    print("❌ 请输入 Long 或 Short")
            
            # 交易结果
            while True:
                try:
                    result_pct = float(input("🎯 交易结果 (%): "))
                    break
                except ValueError:
                    print("❌ 请输入有效的百分比")
            
            # 手续费率
            entry_fee_pct = self.get_float_input("💸 开仓手续费率 (%, 默认0.05): ", 0.05)
            exit_fee_pct = self.get_float_input("💸 平仓手续费率 (%, 默认0.05): ", 0.05)
            
            # 时间
            now = datetime.now()
            default_entry = (now - timedelta(hours=1)).strftime("%Y-%m-%d %H:%M")
            default_exit = now.strftime("%Y-%m-%d %H:%M")
            
            entry_time = input(f"⏰ 开仓时间 (默认: {default_entry}): ").strip()
            if not entry_time:
                entry_time = default_entry
            
            exit_time = input(f"⏰ 平仓时间 (默认: {default_exit}): ").strip()
            if not exit_time:
                exit_time = default_exit
            
            # 可选信息
            instrument = input("📊 交易品种 (可选): ").strip()
            notes = input("📝 备注 (可选): ").strip()
            
            # 计算指标
            metrics = self.calculate_metrics(
                position_size, result_pct, entry_fee_pct, exit_fee_pct,
                entry_time, exit_time
            )
            
            # 显示计算结果
            self.display_calculation(position_size, direction, result_pct, metrics)
            
            # 确认保存
            confirm = input("\n💾 是否保存这条记录? (y/n): ").strip().lower()
            if confirm in ['y', 'yes', '是']:
                # 保存到数据库
                trade_data = {
                    'position_size': position_size,
                    'direction': direction,
                    'result_pct': result_pct,
                    'entry_time': entry_time,
                    'exit_time': exit_time,
                    'entry_fee_pct': entry_fee_pct,
                    'exit_fee_pct': exit_fee_pct,
                    'instrument': instrument,
                    'notes': notes,
                    **metrics
                }
                
                if self.save_trade(trade_data):
                    print("✅ 交易记录保存成功！")
                else:
                    print("❌ 保存失败")
            else:
                print("❌ 记录未保存")
                
        except KeyboardInterrupt:
            print("\n❌ 操作已取消")
        except Exception as e:
            print(f"❌ 添加交易记录失败: {e}")
    
    def get_float_input(self, prompt: str, default: float) -> float:
        """获取浮点数输入"""
        while True:
            try:
                value = input(prompt).strip()
                if not value:
                    return default
                return float(value)
            except ValueError:
                print("❌ 请输入有效的数字")
    
    def display_calculation(self, position_size: float, direction: str, 
                          result_pct: float, metrics: Dict[str, float]):
        """显示计算结果"""
        print("\n📊 计算结果:")
        print("=" * 30)
        print(f"💰 仓位大小: {position_size:,.2f} 元")
        print(f"📈 交易方向: {direction}")
        print(f"🎯 交易结果: {result_pct:+.2f}%")
        print("-" * 20)
        print(f"💵 毛盈亏: {metrics['gross_pnl']:+,.2f} 元")
        print(f"💸 开仓费用: -{metrics['entry_fees']:,.2f} 元")
        print(f"💸 平仓费用: -{metrics['exit_fees']:,.2f} 元")
        print(f"💸 总手续费: -{metrics['total_fees']:,.2f} 元")
        print(f"💰 净盈亏: {metrics['net_pnl']:+,.2f} 元")
        print(f"📈 净收益率: {(metrics['net_pnl']/position_size*100):+.2f}%")
        print(f"⏱️ 持仓时长: {metrics['duration_minutes']:.0f} 分钟")
    
    def save_trade(self, trade_data: Dict[str, Any]) -> bool:
        """保存交易记录"""
        try:
            insert_sql = """
            INSERT INTO trades (
                position_size, direction, result_pct, entry_time, exit_time,
                entry_fee_pct, exit_fee_pct, instrument, notes,
                gross_pnl, entry_fees, exit_fees, total_fees, net_pnl, duration_minutes
            ) VALUES (
                :position_size, :direction, :result_pct, :entry_time, :exit_time,
                :entry_fee_pct, :exit_fee_pct, :instrument, :notes,
                :gross_pnl, :entry_fees, :exit_fees, :total_fees, :net_pnl, :duration_minutes
            )
            """
            
            self.connection.execute(insert_sql, trade_data)
            self.connection.commit()
            return True
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False
    
    def view_trades(self):
        """查看交易记录"""
        try:
            cursor = self.connection.execute("""
                SELECT * FROM trades 
                ORDER BY entry_time DESC 
                LIMIT 10
            """)
            
            trades = cursor.fetchall()
            
            if not trades:
                print("📋 暂无交易记录")
                return
            
            print("\n📋 最近10条交易记录:")
            print("=" * 80)
            print(f"{'ID':<3} {'时间':<16} {'方向':<5} {'仓位':<8} {'结果%':<7} {'净盈亏':<10} {'品种':<10}")
            print("-" * 80)
            
            for trade in trades:
                print(f"{trade['id']:<3} {trade['entry_time'][:16]:<16} "
                      f"{trade['direction']:<5} {trade['position_size']:<8.0f} "
                      f"{trade['result_pct']:+<7.2f} {trade['net_pnl']:+<10.2f} "
                      f"{(trade['instrument'] or '')[:10]:<10}")
            
        except Exception as e:
            print(f"❌ 查看记录失败: {e}")
    
    def show_statistics(self):
        """显示统计信息"""
        try:
            cursor = self.connection.execute("""
                SELECT 
                    COUNT(*) as total_trades,
                    SUM(CASE WHEN net_pnl > 0 THEN 1 ELSE 0 END) as winning_trades,
                    SUM(net_pnl) as total_pnl,
                    AVG(net_pnl) as avg_pnl,
                    MAX(net_pnl) as max_profit,
                    MIN(net_pnl) as max_loss,
                    SUM(total_fees) as total_fees
                FROM trades
            """)
            
            stats = cursor.fetchone()
            
            if stats['total_trades'] == 0:
                print("📊 暂无统计数据")
                return
            
            win_rate = (stats['winning_trades'] / stats['total_trades']) * 100
            
            print("\n📊 交易统计:")
            print("=" * 30)
            print(f"📈 总交易次数: {stats['total_trades']} 笔")
            print(f"🎯 胜率: {win_rate:.1f}%")
            print(f"💰 总盈亏: {stats['total_pnl']:+,.2f} 元")
            print(f"📊 平均盈亏: {stats['avg_pnl']:+,.2f} 元")
            print(f"🚀 最大盈利: {stats['max_profit']:+,.2f} 元")
            print(f"📉 最大亏损: {stats['max_loss']:+,.2f} 元")
            print(f"💸 总手续费: {stats['total_fees']:,.2f} 元")
            
        except Exception as e:
            print(f"❌ 统计信息获取失败: {e}")
    
    def run(self):
        """运行主程序"""
        while True:
            try:
                print("\n" + "=" * 50)
                print("📊 交易复盘工具 - 主菜单")
                print("=" * 50)
                print("1. 📝 添加交易记录")
                print("2. 📋 查看交易记录")
                print("3. 📊 查看统计信息")
                print("4. 🧮 快速计算器")
                print("5. 🚪 退出程序")
                
                choice = input("\n请选择操作 (1-5): ").strip()
                
                if choice == '1':
                    self.add_trade_interactive()
                elif choice == '2':
                    self.view_trades()
                elif choice == '3':
                    self.show_statistics()
                elif choice == '4':
                    self.quick_calculator()
                elif choice == '5':
                    print("👋 感谢使用交易复盘工具！")
                    break
                else:
                    print("❌ 无效选择，请输入1-5")
                    
            except KeyboardInterrupt:
                print("\n👋 程序已退出")
                break
            except Exception as e:
                print(f"❌ 程序错误: {e}")
    
    def quick_calculator(self):
        """快速计算器"""
        print("\n🧮 快速盈亏计算器")
        print("-" * 30)
        
        try:
            position_size = float(input("💰 仓位大小 (元): "))
            result_pct = float(input("🎯 交易结果 (%): "))
            fee_pct = self.get_float_input("💸 手续费率 (%, 默认0.05): ", 0.05)
            
            # 计算
            gross_pnl = position_size * result_pct / 100
            total_fees = position_size * fee_pct / 100 * 2  # 开仓+平仓
            net_pnl = gross_pnl - total_fees
            
            print("\n📊 计算结果:")
            print(f"💵 毛盈亏: {gross_pnl:+,.2f} 元")
            print(f"💸 总手续费: -{total_fees:,.2f} 元")
            print(f"💰 净盈亏: {net_pnl:+,.2f} 元")
            print(f"📈 净收益率: {(net_pnl/position_size*100):+.2f}%")
            
        except ValueError:
            print("❌ 请输入有效的数字")
        except Exception as e:
            print(f"❌ 计算失败: {e}")
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()

def main():
    """主函数"""
    try:
        app = TradingCLI()
        app.run()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
    finally:
        try:
            app.close()
        except:
            pass

if __name__ == "__main__":
    main()
