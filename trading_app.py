"""
交易复盘工具 - 主应用程序
基础原型版本 - 验证技术可行性
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
from database import DatabaseManager
import threading


class TradingApp:
    """交易复盘工具主应用类"""

    def __init__(self):
        """初始化应用程序"""
        self.root = tk.Tk()
        self.db = DatabaseManager()

        # 配置主窗口
        self.setup_main_window()

        # 创建界面
        self.create_widgets()

        # 初始化数据
        self.init_default_values()

        print("🚀 交易复盘工具启动成功")

    def setup_main_window(self):
        """配置主窗口"""
        self.root.title("📊 交易复盘工具 - 原型版本")
        self.root.geometry("800x700")
        self.root.resizable(True, True)

        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")  # 可选
            pass
        except:
            pass

        # 配置样式
        style = ttk.Style()
        style.theme_use('clam')  # 使用现代主题

    def create_widgets(self):
        """创建所有界面组件"""
        # 主标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill='x', padx=20, pady=10)

        title_label = ttk.Label(
            title_frame,
            text="📊 交易复盘工具 - 快速录入",
            font=('Arial', 16, 'bold')
        )
        title_label.pack()

        subtitle_label = ttk.Label(
            title_frame,
            text="验证技术可行性的基础原型",
            font=('Arial', 10)
        )
        subtitle_label.pack()

        # 创建主要内容区域
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # 左侧：输入表单
        self.create_input_form(main_frame)

        # 右侧：实时计算预览
        self.create_calculation_preview(main_frame)

        # 底部：操作按钮和状态
        self.create_bottom_section()

    def create_input_form(self, parent):
        """创建输入表单"""
        # 左侧框架
        left_frame = ttk.LabelFrame(parent, text="📝 交易信息录入", padding=15)
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))

        # 1. 仓位大小
        self.create_position_size_section(left_frame)

        # 2. 交易方向
        self.create_direction_section(left_frame)

        # 3. 交易结果
        self.create_result_section(left_frame)

        # 4. 交易时间
        self.create_time_section(left_frame)

        # 5. 手续费率
        self.create_fees_section(left_frame)

        # 6. 可选字段
        self.create_optional_section(left_frame)

    def create_position_size_section(self, parent):
        """创建仓位大小输入区域"""
        frame = ttk.LabelFrame(parent, text="💰 仓位大小", padding=10)
        frame.pack(fill='x', pady=5)

        # 快速选择按钮
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill='x', pady=5)

        self.position_var = tk.StringVar(value="10000")

        positions = [("5千", "5000"), ("1万", "10000"), ("2万", "20000"), ("5万", "50000")]
        for text, value in positions:
            btn = ttk.Button(
                button_frame,
                text=text,
                command=lambda v=value: self.set_position_size(v),
                width=8
            )
            btn.pack(side='left', padx=2)

        # 自定义输入
        custom_frame = ttk.Frame(frame)
        custom_frame.pack(fill='x', pady=5)

        ttk.Label(custom_frame, text="自定义:").pack(side='left')
        self.position_entry = ttk.Entry(
            custom_frame,
            textvariable=self.position_var,
            width=15
        )
        self.position_entry.pack(side='left', padx=5)
        self.position_entry.bind('<KeyRelease>', self.on_input_change)

        ttk.Label(custom_frame, text="元").pack(side='left')

    def create_direction_section(self, parent):
        """创建交易方向选择区域"""
        frame = ttk.LabelFrame(parent, text="📈 交易方向", padding=10)
        frame.pack(fill='x', pady=5)

        self.direction_var = tk.StringVar(value="Long")

        direction_frame = ttk.Frame(frame)
        direction_frame.pack()

        ttk.Radiobutton(
            direction_frame,
            text="📈 做多 (Long)",
            variable=self.direction_var,
            value="Long",
            command=self.on_input_change
        ).pack(side='left', padx=10)

        ttk.Radiobutton(
            direction_frame,
            text="📉 做空 (Short)",
            variable=self.direction_var,
            value="Short",
            command=self.on_input_change
        ).pack(side='left', padx=10)

    def create_result_section(self, parent):
        """创建交易结果输入区域"""
        frame = ttk.LabelFrame(parent, text="🎯 交易结果", padding=10)
        frame.pack(fill='x', pady=5)

        # 快速选择按钮
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill='x', pady=5)

        self.result_var = tk.StringVar(value="1.0")

        results = [("+0.5%", "0.5"), ("+1.0%", "1.0"), ("+2.0%", "2.0"),
                  ("-0.5%", "-0.5"), ("-1.0%", "-1.0"), ("-2.0%", "-2.0")]

        for i, (text, value) in enumerate(results):
            btn = ttk.Button(
                button_frame,
                text=text,
                command=lambda v=value: self.set_result(v),
                width=8
            )
            row, col = i // 3, i % 3
            btn.grid(row=row, column=col, padx=2, pady=2)

        # 自定义输入
        custom_frame = ttk.Frame(frame)
        custom_frame.pack(fill='x', pady=5)

        ttk.Label(custom_frame, text="自定义:").pack(side='left')
        self.result_entry = ttk.Entry(
            custom_frame,
            textvariable=self.result_var,
            width=10
        )
        self.result_entry.pack(side='left', padx=5)
        self.result_entry.bind('<KeyRelease>', self.on_input_change)

        ttk.Label(custom_frame, text="%").pack(side='left')

    def create_time_section(self, parent):
        """创建时间输入区域"""
        frame = ttk.LabelFrame(parent, text="⏰ 交易时间", padding=10)
        frame.pack(fill='x', pady=5)

        # 默认时间
        now = datetime.now()
        entry_time = now - timedelta(hours=1, minutes=15)
        exit_time = now

        # 开仓时间
        entry_frame = ttk.Frame(frame)
        entry_frame.pack(fill='x', pady=2)

        ttk.Label(entry_frame, text="开仓时间:", width=10).pack(side='left')
        self.entry_time_var = tk.StringVar(value=entry_time.strftime("%Y-%m-%d %H:%M"))
        self.entry_time_entry = ttk.Entry(
            entry_frame,
            textvariable=self.entry_time_var,
            width=20
        )
        self.entry_time_entry.pack(side='left', padx=5)
        self.entry_time_entry.bind('<KeyRelease>', self.on_input_change)

        # 平仓时间
        exit_frame = ttk.Frame(frame)
        exit_frame.pack(fill='x', pady=2)

        ttk.Label(exit_frame, text="平仓时间:", width=10).pack(side='left')
        self.exit_time_var = tk.StringVar(value=exit_time.strftime("%Y-%m-%d %H:%M"))
        self.exit_time_entry = ttk.Entry(
            exit_frame,
            textvariable=self.exit_time_var,
            width=20
        )
        self.exit_time_entry.pack(side='left', padx=5)
        self.exit_time_entry.bind('<KeyRelease>', self.on_input_change)

        # 持仓时长显示
        duration_frame = ttk.Frame(frame)
        duration_frame.pack(fill='x', pady=2)

        ttk.Label(duration_frame, text="持仓时长:", width=10).pack(side='left')
        self.duration_label = ttk.Label(duration_frame, text="75 分钟", foreground='blue')
        self.duration_label.pack(side='left', padx=5)

    def create_fees_section(self, parent):
        """创建手续费率输入区域"""
        frame = ttk.LabelFrame(parent, text="💸 手续费率", padding=10)
        frame.pack(fill='x', pady=5)

        # 开仓手续费
        entry_fee_frame = ttk.Frame(frame)
        entry_fee_frame.pack(fill='x', pady=2)

        ttk.Label(entry_fee_frame, text="开仓费率:", width=10).pack(side='left')
        self.entry_fee_var = tk.StringVar(value="0.05")
        self.entry_fee_entry = ttk.Entry(
            entry_fee_frame,
            textvariable=self.entry_fee_var,
            width=10
        )
        self.entry_fee_entry.pack(side='left', padx=5)
        self.entry_fee_entry.bind('<KeyRelease>', self.on_input_change)
        ttk.Label(entry_fee_frame, text="%").pack(side='left')

        # 平仓手续费
        exit_fee_frame = ttk.Frame(frame)
        exit_fee_frame.pack(fill='x', pady=2)

        ttk.Label(exit_fee_frame, text="平仓费率:", width=10).pack(side='left')
        self.exit_fee_var = tk.StringVar(value="0.05")
        self.exit_fee_entry = ttk.Entry(
            exit_fee_frame,
            textvariable=self.exit_fee_var,
            width=10
        )
        self.exit_fee_entry.pack(side='left', padx=5)
        self.exit_fee_entry.bind('<KeyRelease>', self.on_input_change)
        ttk.Label(exit_fee_frame, text="%").pack(side='left')

    def create_optional_section(self, parent):
        """创建可选字段区域"""
        frame = ttk.LabelFrame(parent, text="📋 可选信息", padding=10)
        frame.pack(fill='x', pady=5)

        # 交易品种
        instrument_frame = ttk.Frame(frame)
        instrument_frame.pack(fill='x', pady=2)

        ttk.Label(instrument_frame, text="交易品种:", width=10).pack(side='left')
        self.instrument_var = tk.StringVar(value="BTC/USDT")
        self.instrument_entry = ttk.Entry(
            instrument_frame,
            textvariable=self.instrument_var,
            width=15
        )
        self.instrument_entry.pack(side='left', padx=5)

        # 备注
        notes_frame = ttk.Frame(frame)
        notes_frame.pack(fill='x', pady=2)

        ttk.Label(notes_frame, text="备注:").pack(anchor='w')
        self.notes_text = tk.Text(notes_frame, height=3, width=40)
        self.notes_text.pack(fill='x', pady=2)

    def create_calculation_preview(self, parent):
        """创建实时计算预览区域"""
        # 右侧框架
        right_frame = ttk.LabelFrame(parent, text="💰 实时计算预览", padding=15)
        right_frame.pack(side='right', fill='both', expand=True)

        # 计算结果显示区域
        self.create_calculation_display(right_frame)

        # 交易记录统计
        self.create_stats_display(right_frame)

    def create_calculation_display(self, parent):
        """创建计算结果显示区域"""
        calc_frame = ttk.LabelFrame(parent, text="📊 当前交易计算", padding=10)
        calc_frame.pack(fill='x', pady=5)

        # 创建计算结果标签
        self.calc_labels = {}

        calc_items = [
            ("gross_pnl", "毛收益:", "+100.00 元"),
            ("entry_fees", "开仓费用:", "-5.00 元"),
            ("exit_fees", "平仓费用:", "-5.00 元"),
            ("total_fees", "总手续费:", "-10.00 元"),
            ("net_pnl", "净收益:", "+90.00 元"),
            ("return_rate", "净收益率:", "+0.90%")
        ]

        for key, label_text, default_value in calc_items:
            frame = ttk.Frame(calc_frame)
            frame.pack(fill='x', pady=3)

            ttk.Label(frame, text=label_text, width=12).pack(side='left')

            value_label = ttk.Label(
                frame,
                text=default_value,
                font=('Arial', 10, 'bold'),
                foreground='green' if '+' in default_value else 'red' if '-' in default_value else 'blue'
            )
            value_label.pack(side='left', padx=10)

            self.calc_labels[key] = value_label

    def create_stats_display(self, parent):
        """创建统计信息显示区域"""
        stats_frame = ttk.LabelFrame(parent, text="📈 数据库统计", padding=10)
        stats_frame.pack(fill='x', pady=5)

        # 总记录数
        count_frame = ttk.Frame(stats_frame)
        count_frame.pack(fill='x', pady=2)

        ttk.Label(count_frame, text="总交易记录:").pack(side='left')
        self.record_count_label = ttk.Label(count_frame, text="0 笔", font=('Arial', 10, 'bold'))
        self.record_count_label.pack(side='left', padx=10)

        # 刷新按钮
        refresh_btn = ttk.Button(
            stats_frame,
            text="🔄 刷新统计",
            command=self.refresh_stats
        )
        refresh_btn.pack(pady=5)

    def create_bottom_section(self):
        """创建底部操作区域"""
        bottom_frame = ttk.Frame(self.root)
        bottom_frame.pack(fill='x', padx=20, pady=10)

        # 操作按钮
        button_frame = ttk.Frame(bottom_frame)
        button_frame.pack(fill='x', pady=5)

        # 保存按钮
        save_btn = ttk.Button(
            button_frame,
            text="💾 保存交易记录",
            command=self.save_trade,
            style='Accent.TButton'
        )
        save_btn.pack(side='left', padx=5)

        # 清空表单按钮
        clear_btn = ttk.Button(
            button_frame,
            text="🗑️ 清空表单",
            command=self.clear_form
        )
        clear_btn.pack(side='left', padx=5)

        # 查看记录按钮
        view_btn = ttk.Button(
            button_frame,
            text="📋 查看所有记录",
            command=self.view_all_trades
        )
        view_btn.pack(side='left', padx=5)

        # 状态栏
        self.status_var = tk.StringVar(value="✅ 应用程序已就绪")
        status_label = ttk.Label(
            bottom_frame,
            textvariable=self.status_var,
            relief='sunken',
            padding=5
        )
        status_label.pack(fill='x', pady=5)

    def init_default_values(self):
        """初始化默认值"""
        # 触发一次计算更新
        self.on_input_change()

        # 更新统计信息
        self.refresh_stats()

    # 事件处理方法
    def set_position_size(self, value):
        """设置仓位大小"""
        self.position_var.set(value)
        self.on_input_change()

    def set_result(self, value):
        """设置交易结果"""
        self.result_var.set(value)
        self.on_input_change()

    def on_input_change(self, event=None):
        """输入变化时的处理"""
        # 延迟执行计算，避免频繁计算
        if hasattr(self, '_calc_timer'):
            self.root.after_cancel(self._calc_timer)

        self._calc_timer = self.root.after(300, self.update_calculations)

    def update_calculations(self):
        """更新实时计算结果"""
        try:
            # 获取输入值
            position_size = float(self.position_var.get() or 0)
            result_pct = float(self.result_var.get() or 0)
            entry_fee_pct = float(self.entry_fee_var.get() or 0)
            exit_fee_pct = float(self.exit_fee_var.get() or 0)
            entry_time = self.entry_time_var.get()
            exit_time = self.exit_time_var.get()

            # 计算指标
            metrics = self.db.calculate_trade_metrics(
                position_size, result_pct, entry_fee_pct, exit_fee_pct,
                entry_time, exit_time
            )

            # 更新显示
            self.calc_labels['gross_pnl'].config(
                text=f"{metrics['gross_pnl']:+.2f} 元",
                foreground='green' if metrics['gross_pnl'] >= 0 else 'red'
            )

            self.calc_labels['entry_fees'].config(
                text=f"-{metrics['entry_fees']:.2f} 元",
                foreground='red'
            )

            self.calc_labels['exit_fees'].config(
                text=f"-{metrics['exit_fees']:.2f} 元",
                foreground='red'
            )

            self.calc_labels['total_fees'].config(
                text=f"-{metrics['total_fees']:.2f} 元",
                foreground='red'
            )

            self.calc_labels['net_pnl'].config(
                text=f"{metrics['net_pnl']:+.2f} 元",
                foreground='green' if metrics['net_pnl'] >= 0 else 'red'
            )

            # 计算净收益率
            return_rate = (metrics['net_pnl'] / position_size * 100) if position_size > 0 else 0
            self.calc_labels['return_rate'].config(
                text=f"{return_rate:+.2f}%",
                foreground='green' if return_rate >= 0 else 'red'
            )

            # 更新持仓时长
            self.duration_label.config(text=f"{metrics['duration_minutes']:.0f} 分钟")

        except ValueError as e:
            # 输入错误时显示默认值
            for key in self.calc_labels:
                self.calc_labels[key].config(text="--", foreground='gray')
            self.duration_label.config(text="-- 分钟")

    def save_trade(self):
        """保存交易记录"""
        try:
            # 验证输入
            if not self.validate_inputs():
                return

            # 收集数据
            trade_data = {
                'position_size': float(self.position_var.get()),
                'direction': self.direction_var.get(),
                'result_pct': float(self.result_var.get()),
                'entry_time': self.entry_time_var.get(),
                'exit_time': self.exit_time_var.get(),
                'entry_fee_pct': float(self.entry_fee_var.get()),
                'exit_fee_pct': float(self.exit_fee_var.get()),
                'instrument': self.instrument_var.get(),
                'notes': self.notes_text.get('1.0', 'end-1c')
            }

            # 保存到数据库
            success = self.db.save_trade(trade_data)

            if success:
                self.status_var.set("✅ 交易记录保存成功！")
                self.refresh_stats()
                messagebox.showinfo("成功", "交易记录已成功保存到数据库！")
            else:
                self.status_var.set("❌ 保存失败")
                messagebox.showerror("错误", "保存交易记录时发生错误！")

        except Exception as e:
            self.status_var.set(f"❌ 保存错误: {str(e)}")
            messagebox.showerror("错误", f"保存失败：{str(e)}")

    def validate_inputs(self):
        """验证输入数据"""
        try:
            # 检查必填字段
            position_size = float(self.position_var.get())
            if position_size <= 0:
                messagebox.showerror("输入错误", "仓位大小必须大于0")
                return False

            result_pct = float(self.result_var.get())
            entry_fee_pct = float(self.entry_fee_var.get())
            exit_fee_pct = float(self.exit_fee_var.get())

            if entry_fee_pct < 0 or exit_fee_pct < 0:
                messagebox.showerror("输入错误", "手续费率不能为负数")
                return False

            # 检查时间格式
            entry_time = self.entry_time_var.get()
            exit_time = self.exit_time_var.get()

            datetime.fromisoformat(entry_time.replace('T', ' '))
            datetime.fromisoformat(exit_time.replace('T', ' '))

            return True

        except ValueError:
            messagebox.showerror("输入错误", "请检查数值格式是否正确")
            return False

    def clear_form(self):
        """清空表单"""
        # 重置为默认值
        self.position_var.set("10000")
        self.direction_var.set("Long")
        self.result_var.set("1.0")

        # 重置时间为当前时间
        now = datetime.now()
        entry_time = now - timedelta(hours=1, minutes=15)
        self.entry_time_var.set(entry_time.strftime("%Y-%m-%d %H:%M"))
        self.exit_time_var.set(now.strftime("%Y-%m-%d %H:%M"))

        self.entry_fee_var.set("0.05")
        self.exit_fee_var.set("0.05")
        self.instrument_var.set("BTC/USDT")
        self.notes_text.delete('1.0', 'end')

        # 更新计算
        self.on_input_change()

        self.status_var.set("🗑️ 表单已清空")

    def refresh_stats(self):
        """刷新统计信息"""
        try:
            count = self.db.get_trade_count()
            self.record_count_label.config(text=f"{count} 笔")

        except Exception as e:
            self.record_count_label.config(text="错误")
            print(f"刷新统计失败: {e}")

    def view_all_trades(self):
        """查看所有交易记录"""
        try:
            trades = self.db.get_all_trades()

            if not trades:
                messagebox.showinfo("信息", "暂无交易记录")
                return

            # 创建新窗口显示记录
            self.show_trades_window(trades)

        except Exception as e:
            messagebox.showerror("错误", f"获取交易记录失败：{str(e)}")

    def show_trades_window(self, trades):
        """显示交易记录窗口"""
        # 创建新窗口
        trades_window = tk.Toplevel(self.root)
        trades_window.title("📋 所有交易记录")
        trades_window.geometry("1000x600")

        # 创建表格
        columns = ('ID', '时间', '方向', '仓位', '结果%', '净盈亏', '品种', '备注')
        tree = ttk.Treeview(trades_window, columns=columns, show='headings', height=20)

        # 设置列标题
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=100)

        # 添加数据
        for trade in trades:
            tree.insert('', 'end', values=(
                trade['id'],
                trade['entry_time'][:16],  # 只显示到分钟
                trade['direction'],
                f"{trade['position_size']:.0f}",
                f"{trade['result_pct']:+.2f}%",
                f"{trade['net_pnl']:+.2f}",
                trade['instrument'] or '',
                (trade['notes'] or '')[:20] + '...' if len(trade['notes'] or '') > 20 else trade['notes'] or ''
            ))

        # 添加滚动条
        scrollbar = ttk.Scrollbar(trades_window, orient='vertical', command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # 关闭按钮
        close_btn = ttk.Button(
            trades_window,
            text="关闭",
            command=trades_window.destroy
        )
        close_btn.pack(pady=10)

    def run(self):
        """运行应用程序"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\n👋 用户中断，正在退出...")
        finally:
            # 清理资源
            self.db.close()
            print("🔚 应用程序已退出")


# 主程序入口
if __name__ == "__main__":
    try:
        print("🚀 启动交易复盘工具...")
        app = TradingApp()
        app.run()
    except Exception as e:
        print(f"❌ 应用程序启动失败: {e}")
        input("按回车键退出...")
