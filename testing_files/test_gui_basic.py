"""
最基础的tkinter GUI测试程序
用于排查GUI显示问题
"""

import tkinter as tk
import sys
import platform

def test_basic_window():
    """测试最基本的tkinter窗口"""
    print("🔍 开始基础GUI测试...")
    print(f"📋 Python版本: {sys.version}")
    print(f"💻 操作系统: {platform.system()} {platform.release()}")
    
    try:
        print("1️⃣ 创建tkinter根窗口...")
        root = tk.Tk()
        print("✅ tkinter根窗口创建成功")
        
        print("2️⃣ 设置窗口属性...")
        root.title("🧪 GUI测试 - 基础版本")
        root.geometry("400x300")
        print("✅ 窗口属性设置成功")
        
        print("3️⃣ 创建标签组件...")
        label = tk.Label(root, text="🎉 GUI测试成功！", font=("Arial", 16))
        label.pack(pady=20)
        print("✅ 标签组件创建成功")
        
        print("4️⃣ 创建按钮组件...")
        def on_button_click():
            print("🖱️ 按钮被点击了！")
            label.config(text="✅ 按钮点击测试成功！")
        
        button = tk.Button(root, text="点击测试", command=on_button_click)
        button.pack(pady=10)
        print("✅ 按钮组件创建成功")
        
        print("5️⃣ 显示窗口并进入主循环...")
        print("🚀 如果您能看到GUI窗口，说明tkinter工作正常！")
        print("❗ 如果看不到窗口，请检查控制台错误信息")
        
        # 强制窗口显示在前台
        root.lift()
        root.attributes('-topmost', True)
        root.after_idle(lambda: root.attributes('-topmost', False))
        
        root.mainloop()
        print("🔚 GUI窗口已关闭")
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        print(f"📝 错误类型: {type(e).__name__}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_basic_window()
